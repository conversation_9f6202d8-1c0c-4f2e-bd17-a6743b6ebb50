/* survey.min.js*/
let currentSection =
  parseInt(sessionStorage.getItem("attemptNumber") || "1", 10) === 1 ? -2 : 0;
let userData = null;
let chart1Instance = null; // Keep this for chart management
let isInitializing = false;

const preSurveyQuestions = {
  title: "Teilnahme am Forschungsprojekt",
  questions: [
    {
      text: "Nehmen Sie im Rahmen des Forschungsprojektes an der Befragung teil?",
      type: "radio",
      options: ["Ja", "Nein"],
      required: true,
    },
    {
      text: "In welcher Gruppe befinden Sie sich?",
      type: "radio",
      options: ["Gruppe A", "Gruppe B", "Gruppe C", "Gruppe D"],
      required: true,
      dependsOn: { questionId: "q-2_0", value: "Ja" },
    },
  ],
};
// -------------------------------------------------------------------------
// Helper: Lock personal fields (for T2/T3 attempts)
function lockPersonalFields() {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const form = document.getElementById("surveyForm");
  form.querySelectorAll("input, select, textarea").forEach((el) => {
    // Skip locking t2_course_feedback and t2_course_list if this is T2
    if (el.name === "t2_course_feedback" || el.name === "t2_course_list") {
      if (attemptNumber === 2) {
        el.required = true; // Required in T2
        return; // Keep editable
      } else if (attemptNumber === 3) {
        el.readOnly = true; // Lock in T3
        el.required = false; // Remove requirement
        el.style.backgroundColor = "#f0f0f0";
        el.style.cursor = "not-allowed";
        return;
      }
    }
    const tag = el.tagName.toLowerCase();
    if (tag === "input") {
      if (el.type === "radio") {
        el.addEventListener("click", function (e) {
          e.preventDefault();
        });
      } else {
        el.readOnly = true;
        el.style.backgroundColor = "#f0f0f0";
        el.style.cursor = "not-allowed";
      }
    } else if (tag === "select") {
      el.disabled = true;
    } else if (tag === "textarea") {
      el.readOnly = true;
      el.style.backgroundColor = "#f0f0f0";
    }
  });
}

// -------------------------------------------------------------------------
// Event Listeners and Utility Functions

function setupEventListeners() {
  const prevBtn = document.getElementById("prevButton");
  const nextBtn = document.getElementById("nextButton");
  const logoutBtn = document.getElementById("logoutButton");
  const saveProgressBtn = document.getElementById("saveProgressButton");
  const surveyForm = document.getElementById("surveyForm");

  if (prevBtn) {
    prevBtn.addEventListener("click", previousSection);
  }
  if (nextBtn) {
    nextBtn.addEventListener("click", nextSection);
  }
  if (logoutBtn) {
    logoutBtn.addEventListener("click", logout);
  }
  if (saveProgressBtn) {
    saveProgressBtn.addEventListener("click", saveAndResumeLater);
  }
  if (surveyForm) {
    // surveyForm.addEventListener('input', function () {  // Removed: No longer needed with PHP backend
    //   saveSectionData(false)
    // })
  }
}

function checkResumeToken() {
  // Removed:  Resume token is not needed with PHP session handling (we'll implement sessions later)
}

/**
 * Moves to the next survey section after validation and saving.
 * Handles pre-survey, datenschutz, regular sections, and the final section transition.
 */
function nextSection() {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  // --- Logic for pre-survey (Section -2, T1 only) ---
  if (currentSection === -2 && attemptNumber === 1) {
    if (validateSection()) {
      const formData = getFormData();
      if (formData["q-2_0"] === "Nein") {
        formData["q-2_1"] = "Gruppe A";
      }
      saveSectionData(false, { preSurveyResponses: formData })
        .then((saveResponse) => {
          if (saveResponse && saveResponse.ok === false) {
            throw new Error(
              saveResponse.message || "Speichern fehlgeschlagen (Serverantwort)"
            );
          }
          currentSection = -1;
          renderSection(currentSection);
          updateProgressBar();
          window.scrollTo({ top: 0, behavior: "smooth" });
        })
        .catch((err) => {
          console.error("Error saving pre-survey data:", err);
          Swal.fire(
            "Fehler",
            `Speichern der Teilnahme-Info fehlgeschlagen: ${
              err.message || "Unbekannter Fehler"
            }`,
            "error"
          );
        });
    }
    return;
  }

  // --- Logic for Datenschutz (Section -1, T1 only) ---
  if (currentSection === -1 && attemptNumber === 1) {
    if (validateSection() && validateDatenschutz()) {
      const consent = document.getElementById("datenschutzConsent").checked;
      const signature = document.getElementById("unterschrift").value;
      saveSectionData(false, {
        datenschutzConsent: consent,
        unterschrift: signature,
      })
        .then((saveResponse) => {
          if (saveResponse && saveResponse.ok === false) {
            throw new Error(
              saveResponse.message || "Speichern fehlgeschlagen (Serverantwort)"
            );
          }
          if (!sessionStorage.getItem("t1_startTimestamp")) {
            const now = new Date().toISOString();
            sessionStorage.setItem("t1_startTimestamp", now);
            if (userData && userData.timeStamps && userData.timeStamps.t1) {
              userData.timeStamps.t1.start = now;
            }
          }
          // --- MODIFIED LOGIC: Find the first valid section to start ---
          let next = 0;
          while (isSectionEmptyForUser(next)) {
            console.log(`Section ${next} is empty, skipping...`);
            next++;
          }
          currentSection = next;
          // --- END MODIFIED LOGIC ---
          renderSection(currentSection);
          updateProgressBar();
          window.scrollTo({ top: 0, behavior: "smooth" });
        })
        .catch((err) => {
          console.error("Error saving Datenschutz consent:", err);
          Swal.fire(
            "Fehler",
            `Speichern der Zustimmung fehlgeschlagen: ${
              err.message || "Unbekannter Fehler"
            }`,
            "error"
          );
        });
    }
    return;
  }

  // --- Main Survey Logic ---
  const lastVisibleSectionIndex =
    attemptNumber === 1 ? surveyData.length - 1 : surveyData.length - 2;

  if (currentSection < lastVisibleSectionIndex) {
    if (validateSection()) {
      saveSectionData(false)
        .then((saveResponse) => {
          if (saveResponse && saveResponse.ok === false) {
            throw new Error(
              saveResponse.message || "Speichern fehlgeschlagen (Serverantwort)"
            );
          }
          // --- MODIFIED LOGIC: Find the next valid section ---
          let next = currentSection + 1;
          while (isSectionEmptyForUser(next)) {
            console.log(`Section ${next} is empty, skipping...`);
            next++;
          }
          currentSection = next;
          // --- END MODIFIED LOGIC ---
          renderSection(currentSection);
          updateProgressBar();
          window.scrollTo({ top: 0, behavior: "smooth" });
        })
        .catch((err) => {
          console.error(
            `Error occurred after validating section ${currentSection}:`,
            err
          );
          Swal.fire({
            icon: "error",
            title: "Speicherfehler",
            text: `Beim Speichern von Abschnitt ${
              currentSection + 1
            } ist ein Fehler aufgetreten: ${
              err.message || "Unbekannter Fehler"
            }`,
          });
        });
    }
  } else {
    // On the last visible section
    if (validateSection()) {
      finishSurvey();
    }
  }
}

function previousSection() {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  let prev = currentSection - 1;

  // For T1, handle special navigation back to pre-survey/datenschutz
  if (attemptNumber === 1) {
    if (currentSection === 0) {
      prev = -1; // Go to Datenschutz
    } else if (currentSection === -1) {
      prev = -2; // Go to Pre-survey
    }
  }

  // --- NEW LOGIC: Skip any empty sections when going backward ---
  // This loop will run for regular survey sections (index >= 0)
  while (prev >= 0 && isSectionEmptyForUser(prev)) {
    console.log(`Section ${prev} is empty, skipping backward...`);
    prev--;
  }
  // --- END NEW LOGIC ---

  // Only update if we have a valid section to go to
  if ((attemptNumber === 1 && prev >= -2) || (attemptNumber > 1 && prev >= 0)) {
    currentSection = prev;
    renderSection(currentSection);
    updateProgressBar();
    window.scrollTo({ top: 0, behavior: "smooth" });
    updateNavigationButtons();
  }
}

function updateProgressBar() {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  if (
    sessionStorage.getItem("surveyCompleted") === "true" &&
    attemptNumber === 1
  ) {
    console.log(
      "Survey is marked complete for T1; skipping progress bar update."
    );
    return;
  }

  let totalSections = 0;
  let currentStep = 0;

  if (attemptNumber === 1) {
    // --- T1 Logic (handles pre-survey and datenschutz) ---
    totalSections = surveyData.length + 2; // All sections + pre-survey + datenschutz
    if (currentSection === -2) {
      currentStep = 1;
    } else if (currentSection === -1) {
      currentStep = 2;
    } else {
      currentStep = currentSection + 3;
    }
  } else {
    // --- NEW, SMARTER T2/T3 Logic ---
    // 1. Create a list of all sections that are actually visible to this user.
    const visibleSectionIndices = [];
    const lastSectionIndex = surveyData.length - 1; // Personal details section

    for (let i = 0; i < lastSectionIndex; i++) {
      if (!isSectionEmptyForUser(i)) {
        visibleSectionIndices.push(i);
      }
    }

    // 2. The total number of steps is the length of our visible sections list.
    totalSections = visibleSectionIndices.length;

    // 3. The current step is the position of the currentSection in our list.
    const currentPositionInList = visibleSectionIndices.indexOf(currentSection);
    if (currentPositionInList !== -1) {
      currentStep = currentPositionInList + 1;
    } else {
      // This is a fallback, it shouldn't happen in normal flow
      currentStep = 0;
    }
  }

  const progress = totalSections > 0 ? (currentStep / totalSections) * 100 : 0;
  const progressFill = document.getElementById("progressFill");
  const progressText = document.getElementById("progressText");

  if (progressFill) {
    progressFill.style.width = `${progress}%`;
    progressFill.setAttribute("aria-valuenow", currentStep);
    progressFill.setAttribute("aria-valuemax", totalSections);
  }
  if (progressText) {
    const displayStep = Math.min(currentStep, totalSections);
    // Only show progress text if there are sections to show
    if (totalSections > 0) {
      progressText.textContent = `Schritt ${displayStep} von ${totalSections}`;
    } else {
      progressText.textContent = ""; // Hide text if there's nothing to show
    }
  }
}

function updateNavigationButtons() {
  // Only hide navigation buttons on the chart generation/results page.
  if (window.location.pathname.includes("results.html")) {
    return;
  }

  const container = document.querySelector(".container");
  if (!container) return;

  // Remove any existing navigation buttons.
  const existingNav = container.querySelector(".navigation-buttons");
  if (existingNav) {
    existingNav.remove();
  }

  const navDiv = document.createElement("div");
  navDiv.className = "navigation-buttons";

  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  if (attemptNumber === 1) {
    if (currentSection === -2) {
      // Pre-survey page: show only "Weiter"
      navDiv.innerHTML = `
        <button type="button" id="nextButton" class="btn btn-primary">
            <i class="fas fa-chevron-right"></i> Weiter
        </button>
      `;
    } else if (currentSection === -1) {
      // Datenschutz page: show "Zurück" and "Weiter"
      navDiv.innerHTML = `
        <button type="button" id="prevButton" class="btn btn-secondary">
            <i class="fas fa-chevron-left"></i> Zurück
        </button>
        <button type="button" id="nextButton" class="btn btn-primary">
            <i class="fas fa-chevron-right"></i> Weiter
        </button>
      `;
    } else {
      // All other survey pages for T1: show "Zurück", "Fortschritt speichern", and "Weiter"
      navDiv.innerHTML = `
        <button type="button" id="prevButton" class="btn btn-secondary">
            <i class="fas fa-chevron-left"></i> Zurück
        </button>
        <button type="button" id="saveProgressButton" class="btn btn-primary">
            <i class="fas fa-save"></i> Fortschritt speichern
        </button>
        <button type="button" id="nextButton" class="btn btn-primary">
            <i class="fas fa-chevron-right"></i> Weiter
        </button>
      `;
    }
  } else {
    // For T2 and T3 (attemptNumber > 1): keep the default behavior (all three buttons)
    navDiv.innerHTML = `
      <button type="button" id="prevButton" class="btn btn-secondary">
          <i class="fas fa-chevron-left"></i> Zurück
      </button>
      <button type="button" id="saveProgressButton" class="btn btn-primary">
          <i class="fas fa-save"></i> Fortschritt speichern
      </button>
      <button type="button" id="nextButton" class="btn btn-primary">
          <i class="fas fa-chevron-right"></i> Weiter
      </button>
    `;
  }

  container.appendChild(navDiv);
  setupEventListeners();
}

function logout() {
  sessionStorage.clear();
  // Removed: localStorage.removeItem('surveyResumeToken') // No longer needed
  userData = null;
  currentSection = -1; // Reset to initial section
  window.location.href = "login.html";
}

function hideNavigationButtons() {
  const navButtons = document.querySelector(".navigation-buttons");
  if (navButtons) {
    navButtons.style.display = "none";
  }
}

function getFormData() {
  console.log(
    "%cExecuting CORRECTED getFormData function!",
    "color: green; font-weight: bold;"
  );
  const form = document.getElementById("surveyForm");
  if (!form) return {};

  const data = {};
  const elements = form.elements;
  const processedGroups = new Set();

  for (let i = 0; i < elements.length; i++) {
    const element = elements[i];
    const name = element.name;

    if (
      !name ||
      element.type === "button" ||
      element.type === "submit" ||
      name.endsWith("_marker") ||
      processedGroups.has(name)
    ) {
      continue;
    }

    if (element.type === "checkbox") {
      if (!processedGroups.has(name)) {
        const checkedBoxes = form.querySelectorAll(
          `input[name="${name}"]:checked`
        );
        const checkedValues = Array.from(checkedBoxes).map((cb) => cb.value);
        data[name] = checkedValues.join(",");
        processedGroups.add(name);
      }
    } else if (name.includes("_blank")) {
      const baseName = name.substring(0, name.lastIndexOf("_blank"));
      if (!processedGroups.has(baseName)) {
        const blankInputs = form.querySelectorAll(
          `input[name^="${baseName}_blank"]`
        );
        const blankValues = Array.from(blankInputs)
          .sort((a, b) => {
            const indexA = parseInt(a.name.substring(baseName.length + 6), 10);
            const indexB = parseInt(b.name.substring(baseName.length + 6), 10);
            return indexA - indexB;
          })
          .map((input) => input.value.trim());
        data[baseName] = blankValues.join("|");
        processedGroups.add(baseName);
      }
    } else if (name.includes("_inline_")) {
      const baseName = name.substring(0, name.lastIndexOf("_inline_"));
      if (!processedGroups.has(baseName)) {
        const inlineInputs = form.querySelectorAll(
          `input[name^="${baseName}_inline_"]`
        );
        const inlineValues = Array.from(inlineInputs)
          .sort((a, b) => {
            const indexA = parseInt(a.name.substring(baseName.length + 8), 10);
            const indexB = parseInt(b.name.substring(baseName.length + 8), 10);
            return indexA - indexB;
          })
          .map((input) => input.value.trim());
        data[baseName] = inlineValues.join("|");
        processedGroups.add(baseName);
      }
    } else if (element.type === "radio") {
      if (element.checked) {
        data[name] = element.value;
        processedGroups.add(name);
      }
    } else {
      let isPartOfProcessedGroup = false;
      processedGroups.forEach((groupName) => {
        if (
          name.startsWith(groupName + "_blank") ||
          name.startsWith(groupName + "_inline_")
        ) {
          isPartOfProcessedGroup = true;
        }
      });

      if (!isPartOfProcessedGroup) {
        data[name] = element.value;
        processedGroups.add(name);
      }
    }
  }

  console.log("Collected Form Data (Corrected):", data);
  return data;
}
function isSectionEmptyForUser(sectionIndex) {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group =
    userData?.preSurveyResponses?.["q-2_1"]?.replace("Gruppe ", "") || "A";

  // Check for invalid section index or special sections
  if (sectionIndex < 0 || sectionIndex >= surveyData.length) {
    return false; // Pre-survey/Datenschutz are never considered empty
  }

  const section = surveyData[sectionIndex];
  if (!section || !section.questions) {
    return true; // No questions means it's empty
  }

  // Check if there is at least one question that should be rendered for this user
  const hasVisibleQuestions = section.questions.some((question) => {
    const isHiddenType = question.type === "hidden";
    if (isHiddenType) return false;

    const groupMatch =
      !question.showOnlyForGroups || question.showOnlyForGroups.includes(group);
    const attemptMatch =
      !question.attempt || question.attempt.includes(attemptNumber);

    return groupMatch && attemptMatch;
  });

  return !hasVisibleQuestions; // If it has no visible questions, the section is empty
}

async function loadUserData() {
  try {
    if (isInitializing) return;
    isInitializing = true;

    const userId = parseInt(sessionStorage.getItem("userId"), 10);
    if (!userId) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "User ID not found. Please log in again.",
      }).then(() => {
        window.location.href = "login.html";
      });
      isInitializing = false; // Reset flag
      return;
    }

    const response = await fetch(`php/get_user_data.php?userId=${userId}`);
    if (!response.ok) throw new Error("Failed to load user data");
    const data = await response.json();

    const attemptNumber = parseInt(
      sessionStorage.getItem("attemptNumber") || "1",
      10
    );

    if (data && Object.keys(data).length > 0) {
      userData = {
        responses: {
          t1: data.initialResponses || {},
          t2: data.updatedResponses || {},
          t3: data.followUpResponses || {},
        },
        scores: {
          t1: data.initialScores || {},
          t2: data.updatedScores || {},
          t3: data.followUpScores || {},
        },
        openEndedResponses: data.openEndedResponses || {},
        attemptNumber: attemptNumber, // Use from session
        datenschutzConsent: data.datenschutzConsent || false,
        unterschrift: data.unterschrift || "",
        timeStamps: data.timeStamps || { t1: {}, t2: {}, t3: {} }, // Ensure structure
        courses: data.courses || [],
        isComplete: data.isComplete || false,
        currentSection:
          attemptNumber > 1
            ? 0 // For T2/T3, always start at section 0 (personal info)
            : data.currentSection !== undefined
            ? data.currentSection
            : -2, // Default for T1
        t1Complete: data.t1Complete || false,
        t2Complete: data.t2Complete || false,
        t3Complete: data.t3Complete || false,
        preSurveyResponses: data.preSurveyResponses || {},
        initialResponses: data.initialResponses || {}, // Keep for reference if needed
        updatedResponses: data.updatedResponses || {},
        followUpResponses: data.followUpResponses || {},
      };

      // Sync sessionStorage startTimestamp if fetched from DB and not in session
      const currentAttemptStartKey = `t${attemptNumber}_startTimestamp`;
      const currentAttemptTimeStamps = userData.timeStamps[`t${attemptNumber}`];
      if (
        currentAttemptTimeStamps &&
        currentAttemptTimeStamps.start &&
        !sessionStorage.getItem(currentAttemptStartKey)
      ) {
        sessionStorage.setItem(
          currentAttemptStartKey,
          currentAttemptTimeStamps.start
        );
        console.log(
          `Loaded startTimestamp for attempt ${attemptNumber} from DB into session.`
        );
      }
    } else {
      // Initialize userData if no data is returned (e.g., new user after registration)
      userData = {
        responses: { t1: {}, t2: {}, t3: {} },
        scores: { t1: {}, t2: {}, t3: {} },
        openEndedResponses: {},
        attemptNumber: attemptNumber,
        datenschutzConsent: false,
        unterschrift: "",
        timeStamps: { t1: {}, t2: {}, t3: {} }, // Ensure structure
        courses: [],
        isComplete: false,
        currentSection: attemptNumber === 1 ? -2 : 0,
        t1Complete: false,
        t2Complete: false,
        t3Complete: false,
        preSurveyResponses: {},
        initialResponses: {},
        updatedResponses: {},
        followUpResponses: {},
      };
    }

    console.log(
      "loadUserData - userData:",
      JSON.parse(JSON.stringify(userData))
    ); // Deep copy for logging

    currentSection = userData.currentSection; // Use the determined currentSection

    // --- NEW LOGIC: Skip any empty sections on initial load ---
    while (isSectionEmptyForUser(currentSection)) {
      console.log(`Initial section ${currentSection} is empty, skipping...`);
      currentSection++;
    }
    // --- END NEW LOGIC ---

    renderSection(currentSection);
    updateProgressBar();
    updateNavigationButtons();
  } catch (error) {
    console.error("Fatal error in loadUserData:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to load user data. Please try again.",
    }).then(() => {
      window.location.href = "login.html";
    });
  } finally {
    isInitializing = false;
  }
}

// survey.min.js - CORRECTED saveSectionData function

async function saveSectionData(isComplete, additionalData = {}) {
  removeUnansweredMarkers();

  const userId = parseInt(sessionStorage.getItem("userId"), 10);
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  if (!userId) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "User ID not found. Please log in again.",
    }).then(() => {
      window.location.href = "login.html";
    });
    return Promise.reject(new Error("No userId found"));
  }

  const processedData = getFormData();
  const formDataToSend = new FormData();

  formDataToSend.append("userId", userId.toString());
  formDataToSend.append("attemptNumber", attemptNumber.toString());
  formDataToSend.append("currentSection", currentSection.toString());
  formDataToSend.append("isComplete", isComplete ? "true" : "false");

  if (additionalData.datenschutzConsent !== undefined) {
    formDataToSend.append(
      "datenschutzConsent",
      additionalData.datenschutzConsent ? "true" : "false"
    );
  }
  if (additionalData.unterschrift) {
    formDataToSend.append("unterschrift", additionalData.unterschrift);
  }
  if (additionalData.preSurveyResponses) {
    try {
      if (
        typeof additionalData.preSurveyResponses === "object" &&
        additionalData.preSurveyResponses !== null
      ) {
        formDataToSend.append(
          "preSurveyResponses",
          JSON.stringify(additionalData.preSurveyResponses)
        );
      }
    } catch (e) {
      console.error("Error stringifying preSurveyResponses:", e);
    }
  }

  for (const key in processedData) {
    if (processedData.hasOwnProperty(key)) {
      if (key.startsWith("q")) {
        formDataToSend.append(key, processedData[key]);
      }
    }
  }

  // Add Timestamps
  const startTimestampKey = `t${attemptNumber}_startTimestamp`;
  const endTimestampKey = `t${attemptNumber}_endTimestamp`;

  const startTimestamp = sessionStorage.getItem(startTimestampKey);
  if (startTimestamp) {
    formDataToSend.append(startTimestampKey, startTimestamp);
  } else if (
    attemptNumber === 1 &&
    currentSection >= 0 &&
    !sessionStorage.getItem(startTimestampKey)
  ) {
    // Fallback: if T1 start wasn't set by login/datenschutz-next, set it now
    const now = new Date().toISOString();
    sessionStorage.setItem(startTimestampKey, now);
    formDataToSend.append(startTimestampKey, now);
    console.warn("T1 startTimestamp was missing, set during saveSectionData.");
  }

  if (isComplete) {
    const endTimestamp = new Date().toISOString();
    formDataToSend.append(endTimestampKey, endTimestamp);
    sessionStorage.setItem(endTimestampKey, endTimestamp); // Save to session for immediate use if needed
  }

  // Append specific open-ended responses if they are part of the current form
  const form = document.getElementById("surveyForm");
  const t2CourseListEl = form?.querySelector("#t2_course_list");
  const t2CourseFeedbackEl = form?.querySelector("#t2_course_feedback");

  if (t2CourseListEl) {
    formDataToSend.append("t2_course_list", t2CourseListEl.value.trim());
  }
  if (t2CourseFeedbackEl) {
    formDataToSend.append(
      "t2_course_feedback",
      t2CourseFeedbackEl.value.trim()
    );
  }
  // Note: Other open-ended (t1_strategy, tX_reflection, etc.) are saved via save-open-ended-response.php

  try {
    console.log("--- Sending FormData to Server (saveSectionData) ---");
    for (let pair of formDataToSend.entries()) {
      console.log(pair[0] + ": " + pair[1]);
    }
    console.log("---------------------------------");

    const res = await fetch("php/save_data.php", {
      method: "POST",
      body: formDataToSend,
    });

    if (!res.ok) {
      let errorMsg = "Server error";
      try {
        const errData = await res.json();
        errorMsg = errData.message || `Server returned status ${res.status}`;
      } catch (e) {
        errorMsg = `Server returned status ${res.status}`;
      }
      throw new Error(errorMsg);
    }
    const data = await res.json();

    // Update local userData
    if (currentSection === -2 && additionalData.preSurveyResponses) {
      userData.preSurveyResponses = {
        ...(userData.preSurveyResponses || {}),
        ...additionalData.preSurveyResponses,
      };
      if (userData.preSurveyResponses["q-2_0"] === "Nein") {
        userData.preSurveyResponses["q-2_1"] = "Gruppe A";
      }
    } else if (currentSection >= 0) {
      const responseKey = `t${attemptNumber}`;
      if (!userData.responses) userData.responses = {};
      userData.responses[responseKey] = {
        ...(userData.responses[responseKey] || {}),
        ...processedData,
      };
    }

    if (isComplete) {
      sessionStorage.setItem("surveyCompleted", "true");
      // Update local userData with timestamps if survey is complete
      if (
        userData &&
        userData.timeStamps &&
        userData.timeStamps[`t${attemptNumber}`]
      ) {
        if (startTimestamp)
          userData.timeStamps[`t${attemptNumber}`].start = startTimestamp;
        userData.timeStamps[`t${attemptNumber}`].end =
          sessionStorage.getItem(endTimestampKey);
      }
    }
    // Also update start timestamp in local userData if it was set
    else if (
      startTimestamp &&
      userData &&
      userData.timeStamps &&
      userData.timeStamps[`t${attemptNumber}`]
    ) {
      userData.timeStamps[`t${attemptNumber}`].start = startTimestamp;
    }

    console.log("Local userData updated after successful save.");
    return data;
  } catch (error) {
    console.error("Error saving user data:", error);
    Swal.fire({
      icon: "error",
      title: "Fehler beim Speichern",
      text:
        error.message ||
        "Beim Speichern Ihrer Daten ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.",
    });
    return Promise.reject(error);
  }
}

async function finishSurvey() {
  console.log("Attempting to finish survey..."); // Log start
  if (validateSection()) {
    try {
      console.log(
        "Validation passed in finishSurvey. Calling saveSectionData(true)..."
      ); // Log before save
      const saveDataResponse = await saveSectionData(true); // Await the save operation
      console.log(
        "saveSectionData(true) completed. Response:",
        saveDataResponse
      ); // Log after save

      // Check if save was explicitly successful if your PHP returns a status
      if (saveDataResponse && saveDataResponse.ok === true) {
        console.log("Save successful. Calling showResults()..."); // Log before showResults
        showResults(); // Call showResults ONLY if save was successful
      } else {
        console.error(
          "saveSectionData(true) reported an issue:",
          saveDataResponse
        );
        Swal.fire(
          "Fehler",
          "Endgültiges Speichern fehlgeschlagen. Bitte versuchen Sie es erneut.",
          "error"
        );
      }
    } catch (err) {
      console.error("Error during finishSurvey:", err); // Log any error during save/show
      Swal.fire({
        icon: "error",
        title: "Fehler beim Abschließen",
        text: "Beim Speichern oder Anzeigen der Ergebnisse ist ein Fehler aufgetreten.",
      });
    }
  } else {
    // This part should ideally be handled by nextSection already, but as a fallback:
    console.log("Validation failed within finishSurvey itself."); // Log validation fail
    const firstInvalid = markUnansweredQuestions(); // Or use the :invalid logic
    if (firstInvalid) {
      firstInvalid.scrollIntoView({ behavior: "smooth", block: "center" });
      // Optionally call reportValidity() or show custom message here too
    }
  }
}

async function saveAndResumeLater() {
  try {
    await saveSectionData(false);
    Swal.fire({
      icon: "success",
      title: "Fortschritt gespeichert",
      text: "Ihr Fortschritt wurde gespeichert. Sie können später fortfahren.",
      timer: 3000,
      showConfirmButton: false,
    });
  } catch (error) {
    console.error("Error saving progress:", error);
    Swal.fire({
      icon: "error",
      title: "Fehler beim Speichern",
      text: "Ihr Fortschritt konnte nicht gespeichert werden. Bitte versuchen Sie es erneut.",
    });
  }
}

function populatePersonalInfo(form, data, lockFields = false) {
  surveyData.forEach((section, sectionIndex) => {
    if (section.title === "Persönliche Angaben") {
      section.questions.forEach((question, questionIndex) => {
        const fieldName = `q${sectionIndex}_${questionIndex}`;
        const value = data[fieldName];
        if (value !== undefined) {
          const fields = form.querySelectorAll(`[name="${fieldName}"]`);
          fields.forEach((field) => {
            if (field.type === "radio") {
              const radioField = form.querySelector(
                `[name="${fieldName}"][value="${value}"]`
              );
              if (radioField) {
                radioField.checked = true;
                if (lockFields) {
                  radioField.addEventListener("click", function (e) {
                    e.preventDefault();
                  });
                }
              }
            } else {
              field.value = value;
              if (lockFields) {
                field.readOnly = true;
                field.style.backgroundColor = "#f0f0f0";
                field.style.cursor = "not-allowed";
              }
            }
          });
        }
      });
    }
  });
}
// Helper function to get the correct response object based on attempt number
function getResponseObject(userData, attemptNumber) {
  if (attemptNumber === 1) {
    return userData.responses.t1 || {};
  } else if (attemptNumber === 2) {
    return userData.responses.t2 || {};
  } else if (attemptNumber === 3) {
    return userData.responses.t3 || {};
  }
  return {}; // Should never happen, but good practice to have a default
}

function isFieldLocked(attemptNumber, sectionIndex, fieldName) {
  // NEW SIMPLIFIED LOGIC:
  // Based on the new survey structure where every attempt requires fresh answers
  // for the knowledge questions and the final demographics page, we will no longer
  // lock any fields. This simplifies the logic and prevents users from being
  // blocked from answering required questions in T2 and T3.
  return false;
}

// Helper to get value with fallback for T2/T3 personal data
// Helper to get value with fallback for T2/T3 personal data
function getPersonalDataValue(userData, attemptNumber, fieldName) {
  if (attemptNumber === 1) {
    return (
      (userData.initialResponses && userData.initialResponses[fieldName]) || ""
    );
  } else if (attemptNumber === 2) {
    return (
      (userData.updatedResponses && userData.updatedResponses[fieldName]) ||
      (userData.initialResponses && userData.initialResponses[fieldName]) ||
      ""
    );
  } else {
    // attemptNumber === 3
    return (
      (userData.followUpResponses && userData.followUpResponses[fieldName]) ||
      (userData.updatedResponses && userData.updatedResponses[fieldName]) ||
      (userData.initialResponses && userData.initialResponses[fieldName]) ||
      ""
    );
  }
}

function populateFormFields(form, userData, sectionIndex) {
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const section = surveyData[sectionIndex];

  if (!section) {
    console.warn(`Section ${sectionIndex} not found in surveyData.`);
    return;
  }

  section.questions.forEach((question, qIndex) => {
    const fieldName = `q${sectionIndex}_${qIndex}`;
    const isLocked = isFieldLocked(attemptNumber, sectionIndex, fieldName);
    let fieldValue = "";

    if (sectionIndex === 0) {
      // Personal Info Section
      fieldValue = getPersonalDataValue(userData, attemptNumber, fieldName);
    } else {
      // Other Sections
      const responses = getResponseObject(userData, attemptNumber);
      fieldValue = responses[fieldName] || "";
    }

    const fields = form.querySelectorAll(`[name="${fieldName}"]`);
    fields.forEach((field) => {
      if (field.type === "radio") {
        if (field.value === fieldValue.toString()) {
          field.checked = true;
        }
        if (isLocked) {
          field.disabled = true; // Disable radio buttons if locked
        }
      } else {
        field.value = fieldValue;
        if (isLocked) {
          field.readOnly = true;
          field.style.backgroundColor = "#f0f0f0";
          field.style.cursor = "not-allowed";
          if (field.tagName.toLowerCase() === "select") {
            field.disabled = true;
          }
        }
      }
    });
  });
}

function validateSemesterInput(input) {
  // Remove any non-digit characters
  input.value = input.value.replace(/\D/g, "");

  const semester = parseInt(input.value, 10);
  const minSemester = 1;
  const maxSemester = 20; // Maximum semester limit is 20
  let isValid = true;
  let message = "";

  if (input.value === "" && input.required) {
    isValid = false;
    message = "Bitte geben Sie Ihr Fachsemester ein.";
  } else if (
    input.value !== "" &&
    (isNaN(semester) || semester < minSemester || semester > maxSemester)
  ) {
    isValid = false;
    message = `Bitte geben Sie ein Semester zwischen ${minSemester} und ${maxSemester} ein.`;
  }

  if (!isValid) {
    input.style.borderColor = "red";
    input.setCustomValidity(message);
  } else {
    input.style.borderColor = ""; // Reset border color
    input.setCustomValidity(""); // Clear custom validity message
  }
}
function validateAbiturInput(input) {
  let value = input.value;
  value = value.replace(/[^0-9.]/g, "");
  const decimalCount = (value.match(/\./g) || []).length;
  if (decimalCount > 1) {
    value = value.substring(0, value.lastIndexOf("."));
  }
  if (value.includes(".")) {
    const parts = value.split(".");
    if (parts[1] && parts[1].length > 1) {
      parts[1] = parts[1].substring(0, 1);
      value = parts.join(".");
    }
  }
  input.value = value;

  const numValue = parseFloat(value);
  const min = 0.0; // Changed from 1.0 to 0.0
  const max = 4.0;
  let isValid = true;
  let message = ""; // Default empty message for valid state

  if (value === "" && input.required) {
    isValid = false;
    message = "Bitte geben Sie Ihren Abiturdurchschnitt ein.";
  } else if (
    value !== "" &&
    (isNaN(numValue) || numValue < min || numValue > max)
  ) {
    // Check only if value is not empty and out of range
    isValid = false;
    message = `Bitte geben Sie einen Wert zwischen ${min.toFixed(
      1
    )} und ${max.toFixed(1)} ein.`;
  }

  if (!isValid) {
    input.setCustomValidity(message);
    input.style.borderColor = "red";
  } else {
    input.setCustomValidity(""); // Valid
    input.style.borderColor = "";
  }
}

function validateSection() {
  const form = document.getElementById("surveyForm");
  if (!form) return false;

  let isValid = true;
  let firstInvalidField = null;

  form.querySelectorAll(".question.unanswered, .unanswered").forEach((q) => {
    q.classList.remove("unanswered");
    q.style.animation = "";
  });
  form.querySelectorAll(".invalid-input").forEach((el) => {
    el.classList.remove("invalid-input");
    el.style.borderColor = "";
  });
  form
    .querySelectorAll(".validation-error-message")
    .forEach((msg) => msg.remove());
  form
    .querySelectorAll("input, select, textarea")
    .forEach((f) => f.setCustomValidity(""));

  const fields = form.querySelectorAll(
    'input:not([type="hidden"]), select, textarea'
  );
  const processedGroups = new Set();

  for (const field of fields) {
    let isVisible = field.offsetParent !== null || field.type === "checkbox";
    if (!isVisible || field.disabled || field.readOnly) {
      field.setCustomValidity("");
      continue;
    }

    let fieldIsValid = true;
    let customMessage = "";
    const questionDiv = field.closest(".question") || field.parentElement;

    if (field.name === "q0_1" && typeof validateYearInput === "function")
      validateYearInput(field);
    else if (
      field.name === "q0_3" &&
      typeof validateSemesterInput === "function"
    )
      validateSemesterInput(field);
    else if (field.name === "q0_4" && typeof validateAbiturInput === "function")
      validateAbiturInput(field);

    const groupName = field.name;
    const baseName = groupName.substring(0, groupName.lastIndexOf("_"));

    if (field.type === "checkbox") {
      let isGroupRequired =
        surveyData[currentSection]?.questions.find((q) => q.id === groupName)
          ?.required || false;

      if (isGroupRequired && !processedGroups.has(groupName)) {
        const groupChecked = form.querySelector(
          `input[name="${groupName}"]:checked`
        );
        if (!groupChecked) {
          fieldIsValid = false;
          customMessage = "Bitte wählen Sie mindestens eine Option aus.";
          if (!firstInvalidField) firstInvalidField = field;
        }
        processedGroups.add(groupName);
      }
    } else if (field.classList.contains("fill-blanks-input")) {
      const fillBlanksBaseName = groupName.substring(
        0,
        groupName.lastIndexOf("_blank")
      );
      let isGroupRequired =
        surveyData[currentSection]?.questions.find(
          (q) => q.id === fillBlanksBaseName
        )?.required || false;

      if (isGroupRequired && !processedGroups.has(fillBlanksBaseName)) {
        const blankInputs = form.querySelectorAll(
          `input[name^="${fillBlanksBaseName}_blank"]`
        );
        let allBlanksFilled = true;
        let firstEmptyBlank = null;
        blankInputs.forEach((blank) => {
          if (blank.value.trim() === "") {
            allBlanksFilled = false;
            if (!firstEmptyBlank) firstEmptyBlank = blank;
          }
        });
        if (!allBlanksFilled) {
          fieldIsValid = false;
          customMessage = "Bitte füllen Sie alle Lücken aus.";
          if (!firstInvalidField) firstInvalidField = firstEmptyBlank;
        }
        processedGroups.add(fillBlanksBaseName);
      }
    } else if (field.name.includes("_inline_")) {
      const inlineBaseName = groupName.substring(
        0,
        groupName.lastIndexOf("_inline_")
      );
      let isGroupRequired =
        surveyData[currentSection]?.questions.find(
          (q) => q.id === inlineBaseName
        )?.required || false;

      if (isGroupRequired && !processedGroups.has(inlineBaseName)) {
        const inlineInputs = form.querySelectorAll(
          `input[name^="${inlineBaseName}_inline_"]`
        );
        let allInlinesFilled = true;
        let firstEmptyInline = null;
        inlineInputs.forEach((inline) => {
          if (inline.value.trim() === "") {
            allInlinesFilled = false;
            if (!firstEmptyInline) firstEmptyInline = inline;
          }
        });
        if (!allInlinesFilled) {
          fieldIsValid = false;
          customMessage = "Bitte füllen Sie alle Felder aus.";
          if (!firstInvalidField) firstInvalidField = firstEmptyInline;
        }
        processedGroups.add(inlineBaseName);
      }
    } else if (field.required && field.value.trim() === "") {
      fieldIsValid = false;
      customMessage = "Bitte füllen Sie dieses Feld aus.";
    }

    if (fieldIsValid && !field.checkValidity()) {
      fieldIsValid = false;
      if (field.validity.rangeOverflow) {
        customMessage = "Der Wert ist zu groß.";
      } else if (field.validity.rangeUnderflow) {
        customMessage = "Der Wert ist zu klein.";
      } else if (field.validity.typeMismatch) {
        customMessage = "Bitte geben Sie einen gültigen Wert ein.";
      } else if (field.validity.patternMismatch) {
        customMessage = "Bitte geben Sie einen Wert im richtigen Format ein.";
      } else {
        customMessage = "Bitte überprüfen Sie Ihre Eingabe.";
      }
    }

    if (!fieldIsValid) {
      isValid = false;
      if (!firstInvalidField) firstInvalidField = field;

      field.setCustomValidity(customMessage);

      let containerToStyle = questionDiv;
      if (field.type === "checkbox")
        containerToStyle = field.closest(".checkbox-group");
      else if (field.classList.contains("fill-blanks-input"))
        containerToStyle = field.closest(".fill-blanks-container");
      else if (field.name.includes("_inline_"))
        containerToStyle = field.closest(".inline-inputs-container");

      if (containerToStyle) containerToStyle.classList.add("unanswered");

      if (field.type !== "checkbox" && field.type !== "radio") {
        field.classList.add("invalid-input");
        field.style.borderColor = "red";
      }

      const errorMsgId = `error-for-${field.name || field.id}`;
      const existingMsg = form.querySelector(`#${errorMsgId}`);
      if (!existingMsg && customMessage) {
        const errorSpan = document.createElement("span");
        errorSpan.id = errorMsgId;
        errorSpan.className = "validation-error-message";
        errorSpan.textContent = customMessage;
        errorSpan.setAttribute("role", "alert");
        const inputGroup =
          field.closest(
            ".radio-group, .checkbox-group, .rating-scale, .fill-blanks-container, .inline-inputs-container"
          ) || field;
        const groupContainer = field.closest(
          ".checkbox-group, .fill-blanks-container, .inline-inputs-container"
        );
        if (groupContainer) {
          if (!groupContainer.querySelector(".validation-error-message")) {
            groupContainer.insertAdjacentElement("afterend", errorSpan);
            field.setAttribute("aria-describedby", errorMsgId);
            field.setAttribute("aria-invalid", "true");
          }
        } else {
          inputGroup.insertAdjacentElement("afterend", errorSpan);
          field.setAttribute("aria-describedby", errorMsgId);
          field.setAttribute("aria-invalid", "true");
        }
      }
    } else {
      field.setCustomValidity("");
      field.classList.remove("invalid-input");
      field.style.borderColor = "";
      field.removeAttribute("aria-invalid");
      field.removeAttribute("aria-describedby");

      let containerToClear =
        field.closest(
          ".question, .checkbox-group, .fill-blanks-container, .inline-inputs-container"
        ) || field.parentElement;

      if (containerToClear) {
        containerToClear.classList.remove("unanswered");
      }

      const errorMsgId = `error-for-${field.name || field.id}`;
      const existingMsg = form.querySelector(`#${errorMsgId}`);
      if (existingMsg) existingMsg.remove();
    }
  }

  if (!isValid && firstInvalidField) {
    console.log(
      "Validation FAILED. First invalid field:",
      firstInvalidField.name || firstInvalidField.id
    );
    firstInvalidField.scrollIntoView({ behavior: "smooth", block: "center" });
    setTimeout(() => firstInvalidField.focus({ preventScroll: true }), 100);
    const parentContainer =
      firstInvalidField.closest(".question") ||
      firstInvalidField.closest(
        ".checkbox-group, .fill-blanks-container, .inline-inputs-container"
      ) ||
      firstInvalidField.parentElement;
    if (parentContainer) {
      parentContainer.classList.add("shake-animation");
      parentContainer.addEventListener(
        "animationend",
        () => {
          parentContainer.classList.remove("shake-animation");
        },
        { once: true }
      );
    }
  }

  console.log("validateSection final result:", isValid);
  return isValid;
}

function validateDatenschutz() {
  const checks = [
    {
      el: document.getElementById("datenschutzConsent"),
      label: "Datenschutzerklärung",
    },
    { el: document.getElementById("unterschrift"), label: "Unterschrift" }, // Keep this for consistent structure
  ];

  let valid = true;

  // Remove existing 'unanswered' classes and shake animations, and messages
  checks.forEach(({ el }) => {
    if (el?.parentElement) {
      el.parentElement.classList.remove("unanswered");
      el.parentElement.style.animation = ""; // Remove shake
      const existingMessage = el.parentElement.querySelector(
        ".unanswered-message"
      );
      if (existingMessage) {
        existingMessage.remove();
      }
    }
  });

  // Validate each field
  for (const { el, label } of checks) {
    //Destructure label here
    if (!el) {
      valid = false;
      continue; // Skip to the next check if element is missing
    }

    let isFieldValid = true;
    if (el.type === "checkbox") {
      isFieldValid = el.checked;
    } else if (el.id === "unterschrift") {
      //For the unterschrift, we just need to check if it exists,
      //as it is prefilled and readonly
      isFieldValid = el.value.trim() !== "";
    } else {
      isFieldValid = el.value.trim() !== "";
    }

    if (!isFieldValid) {
      valid = false;
      if (el.parentElement) {
        el.parentElement.classList.add("unanswered");
        el.parentElement.style.animation = "shake 0.3s"; // Add shake

        // Add the error message
        const messageSpan = document.createElement("span");
        messageSpan.className = "unanswered-message";
        messageSpan.textContent = `⚠️ Bitte ${label} bestätigen.`; // Use the label
        el.parentElement.appendChild(messageSpan);
      }
    }
  }

  return valid;
}

function validateYearInput(input) {
  // Remove any non-digit characters
  input.value = input.value.replace(/\D/g, "");

  // Ensure it doesn't exceed maxlength
  if (input.value.length > 4) {
    input.value = input.value.slice(0, 4);
  }

  const year = parseInt(input.value, 10);
  const currentYear = new Date().getFullYear();
  const minYear = 1930; // Reasonable minimum birth year
  const maxYear = currentYear - 16; // Must be at least 16 years old (2009-2025 requirement)
  let isValid = true;
  let message = "";

  if (input.value === "" && input.required) {
    isValid = false;
    message = "Bitte geben Sie Ihr Geburtsjahr ein.";
  } else if (input.value.length > 0 && input.value.length < 4) {
    isValid = false; // Invalid if not empty and not 4 digits
    message = "Bitte geben Sie eine 4-stellige Jahreszahl ein.";
  } else if (input.value.length === 4) {
    if (isNaN(year) || year < minYear || year > maxYear) {
      isValid = false;
      message = `Bitte geben Sie ein Jahr zwischen ${minYear} und ${maxYear} ein.`;
    }
  }

  if (!isValid) {
    input.style.borderColor = "red";
    input.setCustomValidity(message);
  } else {
    input.style.borderColor = ""; // Reset border color
    input.setCustomValidity(""); // Clear custom validity message
  }
}
function renderFillBlanksInput(
  questionText,
  fieldName,
  savedValue = "",
  isLocked = false,
  isRequired = false
) {
  const escapedQuestionText = questionText
    .replace(/</g, "<")
    .replace(/>/g, ">");
  const parts = escapedQuestionText.split("____");
  let html = '<div class="fill-blanks-container">';
  const savedAnswers = savedValue ? String(savedValue).split("|") : []; // Ensure string before split
  const readonlyStyle = isLocked
    ? 'readonly style="background-color: #eee;"'
    : "";
  const requiredAttr = isRequired ? "required" : "";

  parts.forEach((part, index) => {
    let textPart = part;
    // Bold the first sentence of the first part
    if (index === 0) {
      const firstSentenceEnd = textPart.indexOf(":"); // Assuming the sentence ends before the first colon introducing terms
      if (firstSentenceEnd !== -1) {
        textPart = `<strong>${textPart.substring(
          0,
          firstSentenceEnd + 1
        )}</strong>${textPart.substring(firstSentenceEnd + 1)}`;
      } else {
        // Fallback if no colon, bold the whole first part if it's short or up to a period.
        const firstPeriod = textPart.indexOf(".");
        if (firstPeriod !== -1 && firstPeriod < 100) {
          // Arbitrary length check
          textPart = `<strong>${textPart.substring(
            0,
            firstPeriod + 1
          )}</strong>${textPart.substring(firstPeriod + 1)}`;
        } else if (textPart.length < 100) {
          textPart = `<strong>${textPart}</strong>`;
        }
      }
    }

    html += `<span class="fill-blanks-text">${textPart}</span>`;
    if (index < parts.length - 1) {
      const blankValue = savedAnswers[index] || "";
      const escapedBlankValue = blankValue.replace(/"/g, '"');
      html += `<input type="text" class="fill-blanks-input" name="${fieldName}_blank${index}" value="${escapedBlankValue}" ${requiredAttr} ${readonlyStyle}>`;
    }
  });
  html += "</div>";
  return html;
}

function setupSingleChoiceCheckboxes() {
  const section = surveyData[currentSection];
  if (!section || !section.questions) return;

  section.questions.forEach((question) => {
    // Find questions that are checkboxes and have our new flag
    if (question.type === "checkbox" && question.singleChoice) {
      const groupName = question.id;
      const checkboxes = document.querySelectorAll(
        `input[type="checkbox"][name="${groupName}"]`
      );

      checkboxes.forEach((checkbox) => {
        checkbox.addEventListener("change", (event) => {
          // If this box was just checked, uncheck all others in the same group
          if (event.target.checked) {
            checkboxes.forEach((otherCheckbox) => {
              if (otherCheckbox !== event.target) {
                otherCheckbox.checked = false;
              }
            });
          }
        });
      });
    }
  });
}

function renderSection(sectionIndex) {
  const surveyForm = document.getElementById("surveyForm");
  if (!surveyForm) {
    console.error("Survey form container not found!");
    return;
  }
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group = userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";

  console.log(
    `Rendering section: ${sectionIndex}, Attempt: ${attemptNumber}, Group: ${group}`
  );

  surveyForm.innerHTML = ""; // Clear previous content

  if (attemptNumber > 1 && (sectionIndex === -2 || sectionIndex === -1)) {
    console.log(
      `Attempt ${attemptNumber}: Skipping pre-survey/datenschutz, redirecting to section 0.`
    );
    currentSection = 0;
    sectionIndex = 0;
  }

  // --- NEW: Skip empty sections right at the start of rendering ---
  if (isSectionEmptyForUser(sectionIndex)) {
    console.warn(
      `Section ${sectionIndex} is empty for this user, skipping render.`
    );
    // This is a safeguard; the main skipping logic is in the navigation functions.
    // If we somehow land here, we should try to move to the next valid section.
    let next = sectionIndex + 1;
    while (isSectionEmptyForUser(next)) {
      next++;
    }
    currentSection = next;
    // Re-call renderSection with the new, valid index
    renderSection(currentSection);
    return;
  }

  if (sectionIndex === -2 && attemptNumber === 1) {
    console.log("Rendering Pre-Survey Section (-2)");
    let html = `<div class="section"><h2>${preSurveyQuestions.title}</h2>`;

    if (preSurveyQuestions.instruction) {
      html += `<p class="section-instruction">${preSurveyQuestions.instruction
        .replace(/</g, "<")
        .replace(/>/g, ">")}</p>`;
    }

    const q0 = preSurveyQuestions.questions[0];
    const savedQ0Value = userData?.preSurveyResponses?.["q-2_0"] || "";
    html += `<div class="question" id="q-2_0-container">
        <p>${q0.text.replace(/</g, "<").replace(/>/g, ">")}</p>
        <div class="radio-group" id="q-2_0-group">`;
    q0.options.forEach((opt) => {
      const isChecked = savedQ0Value === opt;
      const escapedOpt = opt.replace(/</g, "<").replace(/>/g, ">");
      html += `<div class="radio-option">
          <label>
            <input type="radio" name="q-2_0" value="${opt}" ${
        isChecked ? "checked" : ""
      } ${q0.required ? "required" : ""} onchange="handlePreSurveyChange(this)">
            <span class="radio-checkmark"></span>
            ${escapedOpt}
          </label>
        </div>`;
    });
    html += `</div></div>`;

    const q1 = preSurveyQuestions.questions[1];
    const savedQ1Value = userData?.preSurveyResponses?.["q-2_1"] || "";
    const showGroupQuestion = savedQ0Value === "Ja";
    html += `<div class="question" id="q-2_1-container" style="display: ${
      showGroupQuestion ? "block" : "none"
    };">
        <p>${q1.text.replace(/</g, "<").replace(/>/g, ">")}</p>
        <div class="radio-group" id="q-2_1-group">`;
    q1.options.forEach((opt) => {
      const isChecked = savedQ1Value === opt;
      const requiredAttr = q1.required && showGroupQuestion ? "required" : "";
      const escapedOpt = opt.replace(/</g, "<").replace(/>/g, ">");
      html += `<div class="radio-option">
          <label>
            <input type="radio" name="q-2_1" value="${opt}" ${
        isChecked ? "checked" : ""
      } ${requiredAttr}>
            <span class="radio-checkmark"></span>
            ${escapedOpt}
          </label>
        </div>`;
    });
    html += `</div></div>`;

    html += `</div>`;
    surveyForm.innerHTML = html;

    const participationRadio = surveyForm.querySelector(
      'input[name="q-2_0"]:checked'
    );
    if (participationRadio) {
      handlePreSurveyChange(participationRadio);
    }
  } else if (sectionIndex === -1 && attemptNumber === 1) {
    console.log("Rendering Datenschutz Section (-1)");
    const savedConsent = userData?.datenschutzConsent || false;
    const consentChecked = savedConsent;
    const savedUnterschrift =
      userData?.unterschrift || sessionStorage.getItem("generatedCode") || "";
    const currentDate = new Date().toISOString().split("T")[0];
    const escapedUnterschrift = savedUnterschrift.replace(/"/g, '"');

    surveyForm.innerHTML = `
          <div class="datenschutz-section">
            <h2>Datenschutzerklärung</h2>
            <p>Danke, dass Sie an dieser Studie teilnehmen. Bevor Sie starten, müssen wir sicherstellen, dass wir Ihre Daten speichern dürfen. Dafür lesen Sie sich bitte die Datenschutzerklärung durch und stimmen Sie dieser durch Ihre digitale Unterschrift zu.</p>
            <div class="datenschutz-content">
              <h3>Projektleitung:</h3>
              <p>Prof.in Dr. Charlott Rubach & Anne-Kathrin Hirsch</p>
              <p>Sehr geehrte Studierende,</p>
              <p>die Digitalisierung und Digitalität erhielten in den letzten Jahren große Aufmerksamkeit. Der kompetente Umgang mit digitalen Medien gehört zum Aufgabenbereich in vielen Berufsfeldern dazu. Daher ist es bedeutsam, dass Studierende während ihrer Ausbildung an der Universität auf diesen Umgang vorbereitet werden. Wir interessieren uns im Rahmen dieser Studie „Open-Digi" dafür, inwieweit die von uns erstellten Lernerfahrungen zur Förderung digitaler Kompetenzen beitragen.</p>
              <p>Wir sind Prof. Dr. Charlott Rubach und Anne-Kathrin Hirsch, Bildungsforscherinnen an der Universität Rostock. Unsere Forschungsschwerpunkte sind Digitalisierung, Förderung digitaler Kompetenzen und Gestaltungsmöglichkeiten einer bedarfsorientierten Lehrkräftebildung.</p>
              <p>Ziel des Projektes ist die Untersuchung von effektiven Lernerfahrungen für die Entwicklung digitaler Kompetenzen. Das Projekt besteht aus mehreren Schritten:</p>
              <ul>
                <li>Sie füllen die Befragung zum Open-Digi Projekt aus, welcher der Pre-Diagnostik gilt und zirka 20 Minuten dauert. Alle Befragungen thematisieren ausschließlich Aspekte von digitaler Kompetenz.</li>
                <li>Ihnen werden auf Grundlage der Diagnostik mehrere Vorschläge gemacht, wie Sie eigene Kompetenzen weiterentwickeln können.</li>
                <li>Sie bearbeiten verschiedene Mikrofortbildungen.</li>
                <li>Sie durchlaufen die Post-Diagnostik direkt nach Bearbeitung der Kurse.</li>
                <li>Sie machen eine dritte Befragung, 1 Monat nach Bearbeitung der Kurse.</li>
              </ul>
              <p>Ihre Teilnahme an unserer Studie ist freiwillig. Wenn Sie an der Studie teilnehmen, können Sie die Befragung jederzeit abbrechen. In diesem Falle werden die Daten nicht gespeichert.</p>
              <p>Die Befragung ist anonym. Das heißt, es werden auch ausschließlich anonymisierte Informationen analysiert und im Rahmen wissenschaftlicher Arbeiten veröffentlicht. Es werden keine Informationen gespeichert, die es uns möglich machen, Sie als Person zu identifizieren. Eine Rücknahme Ihres Einverständnisses und damit Löschung Ihrer Daten, nachdem Sie den Fragebogen ausgefüllt und abgegeben haben, ist demnach nicht möglich. Anonymisiert sind auch Daten, die keine persönliche Information mehr enthalten, bspw. Alter, Geschlecht, Abiturnote, Studienrichtung und Hochschulsemester.</p>
              <p>Wir speichern Ihre Antworten und Ihre Angaben (z. B. Alter und Geschlecht). Diese werden bis zum Abschluss der Untersuchung und maximal 10 Jahre auf den Dienstrechnern der Wissenschaftlerinnen aus dem Projekt gespeichert und danach gelöscht.</p>
              <p>Es erfolgt keine Weitergabe Ihrer Daten an Dritte außerhalb des Forschungsprojektes.</p>
            </div>
            <div class="datenschutz-inputs">
              <div class="question">
                <label for="datum">Datum</label>
                <input type="date" id="datum" name="datum" value="${currentDate}" required readonly style="background-color: #eee;"></input>
              </div>
              <div class="question">
                <label for="unterschrift">Unterschrift</label>
                <input type="text" id="unterschrift" name="unterschrift" value="${escapedUnterschrift}" readonly required style="background-color: #eee;"></input>
              </div>
              <div class="agreement-questions">
                <div class="agreement">
                  <label>
                    <input type="checkbox" id="datenschutzConsent" name="datenschutzConsent" ${
                      consentChecked ? "checked" : ""
                    } required></input>
                    <p>Die <a href="datenschutz.html" target="_blank">Datenschutzerklärung</a> ist mir zur Kenntnis gelangt. Ich bin mit der dort näher beschriebenen Verarbeitung meiner personenbezogene Daten einverstanden.</p>
                  </label>
                </div>
              </div>
            </div>
          </div>
        `;
  } else if (sectionIndex >= 0 && sectionIndex < surveyData.length) {
    const section = surveyData[sectionIndex];
    const isDemographicsSection = section.title === "Persönliche Angaben";

    let html = `<div class="section"><h2>${section.title
      .replace(/</g, "<")
      .replace(/>/g, ">")}</h2>`;

    // --- THIS IS THE MODIFIED LOGIC FOR CHOOSING THE INSTRUCTION ---
    let sectionInstruction = section.instruction; // Default instruction
    if (sectionIndex === 0 && attemptNumber === 2) {
      // Check for the most specific instruction first
      if (group === "Gruppe D" && section.instruction_t2_group_d) {
        sectionInstruction = section.instruction_t2_group_d;
      } else if (group === "Gruppe C" && section.instruction_t2_group_c) {
        sectionInstruction = section.instruction_t2_group_c;
      } else if (section.instruction_t2) {
        // Fallback to the general T2 instruction
        sectionInstruction = section.instruction_t2;
      }
    }
    // --- END OF MODIFIED LOGIC ---

    if (sectionInstruction) {
      html += `<p class="section-instruction">${sectionInstruction
        .replace(/</g, "<")
        .replace(/>/g, ">")}</p>`;
    }

    const questionsToRender = section.questions.filter((question) => {
      if (question.type === "hidden") return false;
      if (
        question.showOnlyForGroups &&
        Array.isArray(question.showOnlyForGroups) &&
        !question.showOnlyForGroups.includes(group.replace("Gruppe ", ""))
      )
        return false;

      if (sectionIndex === 0) {
        return question.attempt && question.attempt.includes(attemptNumber);
      }
      return true;
    });

    questionsToRender.forEach((question) => {
      const fieldName = question.id;
      const isLocked = isDemographicsSection
        ? false
        : isFieldLocked(attemptNumber, sectionIndex, fieldName);
      let fieldValue = "";

      if (!isDemographicsSection) {
        const responses = getResponseObject(userData, attemptNumber);
        fieldValue = responses[fieldName] || "";
      }

      if (question.type === "instruction") {
        const instructionText = question.text.replace(/\n/g, "<br>");
        html += `<p class="section-instruction competency-task-instruction">${instructionText}</p>`;
      } else {
        html += `<div class="question" id="${fieldName}-container">`;

        if (question.type !== "fill-blanks") {
          if (question.isHtmlPrompt) {
            html += `<p>${question.text}</p>`;
          } else {
            const escapedPrompt = question.text
              .replace(/</g, "<")
              .replace(/>/g, ">");
            html += `<p>${escapedPrompt}</p>`;
          }
        }

        const readonlyStyle = isLocked
          ? 'readonly style="background-color: #eee;"'
          : "";
        const disabledAttr = isLocked ? "disabled" : "";
        const requiredAttr = question.required ? "required" : "";

        if (question.type === "radio") {
          html += `<div class="radio-group" id="${fieldName}-group">`;
          question.options.forEach((opt) => {
            const isChecked = fieldValue === opt;
            const escapedOpt = opt.replace(/</g, "<").replace(/>/g, ">");
            html += `
                        <div class="radio-option">
                          <label>
                            <input type="radio" name="${fieldName}" value="${opt}"
                                   ${isChecked ? "checked" : ""}
                                   ${requiredAttr}
                                   ${disabledAttr}
                                   ${
                                     fieldName === "q0_2"
                                       ? 'onchange="handleTeachingStudentChange(this)"'
                                       : ""
                                   }>
                            <span class="radio-checkmark"></span>
                            ${escapedOpt}
                          </label>
                        </div>`;
          });
          html += `</div>`;

          if (fieldName === "q0_2") {
            const showLehramtFields = fieldValue === "Ja";
            const showOtherStudiesField = fieldValue === "Nein";
            const lehramtValue = getPersonalDataValue(
              userData,
              attemptNumber,
              "q0_6"
            );
            const faecherValue = getPersonalDataValue(
              userData,
              attemptNumber,
              "q0_7"
            );
            const otherStudiesValue = getPersonalDataValue(
              userData,
              attemptNumber,
              "q0_8"
            );
            const lehramtLocked = isFieldLocked(attemptNumber, 0, "q0_6");
            const faecherLocked = isFieldLocked(attemptNumber, 0, "q0_7");
            const otherStudiesLocked = isFieldLocked(attemptNumber, 0, "q0_8");
            const lehramtRequired =
              !lehramtLocked && showLehramtFields && question.required
                ? "required"
                : "";
            const faecherRequired =
              !faecherLocked && showLehramtFields && question.required
                ? "required"
                : "";
            const otherStudiesRequired =
              !otherStudiesLocked && showOtherStudiesField && question.required
                ? "required"
                : "";
            const escapedFaecherValue = faecherValue.replace(/"/g, '"');
            const escapedOtherStudiesValue = otherStudiesValue.replace(
              /"/g,
              '"'
            );

            html += `
                 <div class="conditional-fields-container">
                   <div class="conditional-field" data-condition="Ja" style="display: ${
                     showLehramtFields ? "block" : "none"
                   };">
                     <div class="question">
                       <label>Welches Lehramt studieren Sie?</label>
                       <select name="q0_6" ${
                         lehramtLocked ? "disabled" : ""
                       } ${lehramtRequired}>
                         <option value="" disabled ${
                           !lehramtValue ? "selected" : ""
                         }>Bitte wählen</option>
                         <option value="Lehramt an Grundschulen" ${
                           lehramtValue === "Lehramt an Grundschulen"
                             ? "selected"
                             : ""
                         }>Lehramt an Grundschulen</option>
                         <option value="Lehramt an Regionalen Schulen" ${
                           lehramtValue === "Lehramt an Regionalen Schulen"
                             ? "selected"
                             : ""
                         }>Lehramt an Regionalen Schulen</option>
                         <option value="Lehramt an Gymnasien" ${
                           lehramtValue === "Lehramt an Gymnasien"
                             ? "selected"
                             : ""
                         }>Lehramt an Gymnasien</option>
                         <option value="Lehramt für Sonderpädagogik" ${
                           lehramtValue === "Lehramt für Sonderpädagogik"
                             ? "selected"
                             : ""
                         }>Lehramt für Sonderpädagogik</option>
                         <option value="Berufspädagogik (B.A.)" ${
                           lehramtValue === "Berufspädagogik (B.A.)"
                             ? "selected"
                             : ""
                         }>Berufspädagogik (B.A.)</option>
                         <option value="Berufspädagogik (M.A.)" ${
                           lehramtValue === "Berufspädagogik (M.A.)"
                             ? "selected"
                             : ""
                         }>Berufspädagogik (M.A.)</option>
                         <option value="Wirtschaftspädagogik (B.A.)" ${
                           lehramtValue === "Wirtschaftspädagogik (B.A.)"
                             ? "selected"
                             : ""
                         }>Wirtschaftspädagogik (B.A.)</option>
                         <option value="Wirtschaftspädagogik (M.A.)" ${
                           lehramtValue === "Wirtschaftspädagogik (M.A.)"
                             ? "selected"
                             : ""
                         }>Wirtschaftspädagogik (M.A.)</option>
                       </select>
                     </div>
                     <div class="question">
                       <label>Welche Fächer studieren Sie aktuell in Ihrem Lehramtsstudium?</label>
                       <input type="text" name="q0_7" ${
                         faecherLocked
                           ? 'readonly style="background-color: #eee;"'
                           : ""
                       } ${faecherRequired} value="${escapedFaecherValue}"></input>
                     </div>
                   </div>
                   <div class="conditional-field" data-condition="Nein" style="display: ${
                     showOtherStudiesField ? "block" : "none"
                   };">
                     <div class="question">
                       <label>Was studieren Sie?</label>
                       <input type="text" name="q0_8" ${
                         otherStudiesLocked
                           ? 'readonly style="background-color: #eee;"'
                           : ""
                       } ${otherStudiesRequired} value="${escapedOtherStudiesValue}"></input>
                     </div>
                   </div>
                 </div>`;
          }
        } else if (question.type === "dropdown") {
          html += `<select name="${fieldName}" ${requiredAttr} ${disabledAttr}>`;
          html += `<option value="" disabled ${
            !fieldValue ? "selected" : ""
          }>Bitte wählen</option>`;
          question.options.forEach((opt) => {
            const escapedOpt = opt.replace(/</g, "<").replace(/>/g, ">");
            html += `<option value="${opt}" ${
              fieldValue === opt ? "selected" : ""
            }>${escapedOpt}</option>`;
          });
          html += `</select>`;
          if (isLocked) {
            const escapedFieldValue = String(fieldValue).replace(/"/g, '"');
            html += `<input type="hidden" name="${fieldName}" value="${escapedFieldValue}"></input>`;
          }
        } else if (question.type === "number" || question.type === "text") {
          let inputType = question.type;
          let specificAttrs = "";
          const escapedFieldValue = String(fieldValue).replace(/"/g, '"');

          if (fieldName === "q0_1") {
            inputType = "text";
            specificAttrs = `maxlength="4" inputmode="numeric" pattern="\\d{4}" oninput="validateYearInput(this)"`;
          } else if (fieldName === "q0_4") {
            inputType = "text";
            specificAttrs = `inputmode="decimal" pattern="^[0-4](\\.[0-9])?$" oninput="validateAbiturInput(this)" placeholder="z.B. 2.3"`;
          } else if (fieldName === "q0_3") {
            inputType = "number";
            specificAttrs = `min="1" max="99" step="1" oninput="validateSemesterInput(this)"`;
          } else if (question.type === "number") {
            specificAttrs = `min="${question.min || ""}" max="${
              question.max || ""
            }" step="${
              question.step || "any"
            }" oninput="validateNumberInput(this)"`;
          }
          html += `<input type="${inputType}" name="${fieldName}" value="${escapedFieldValue}" ${specificAttrs} ${requiredAttr} ${readonlyStyle}></input>`;
        } else if (question.type === "scale") {
          html += `<div class="rating-scale">`;
          for (let i = 0; i <= 6; i++) {
            const isSelected = fieldValue === i.toString();
            html += `
                         <label class="scale-label">
                           <input type="radio" name="${fieldName}" value="${i}"
                                  ${isSelected ? "checked" : ""}
                                  ${requiredAttr}
                                  ${disabledAttr}>
                           <span class="scale-button" role="button" tabindex="${
                             isLocked ? -1 : 0
                           }" onclick="handleScaleClick(event)" onkeydown="handleScaleKeydown(event)">${i}</span>
                         </label>
                       `;
          }

          let labelStart = "stimme gar nicht zu";
          let labelEnd = "stimme voll und ganz zu";
          if (sectionIndex >= 1 && sectionIndex <= 6) {
            labelStart = "gar nicht kompetent";
            labelEnd = "ausgesprochen kompetent";
          }

          html += `</div><div class="scale-labels"><span>${labelStart}</span><span>${labelEnd}</span></div>`;
        } else if (question.type === "textarea") {
          const escapedFieldValue = String(fieldValue)
            .replace(/</g, "<")
            .replace(/>/g, ">");
          html += `<textarea name="${fieldName}" rows="4" ${requiredAttr} ${readonlyStyle}>${escapedFieldValue}</textarea>`;
        } else if (question.type === "checkbox") {
          html += `<div class="checkbox-group" id="${fieldName}-group">`;
          const savedOptions = fieldValue ? String(fieldValue).split(",") : [];
          question.options.forEach((opt) => {
            const isChecked = savedOptions.includes(opt);
            const escapedOpt = opt.replace(/</g, "<").replace(/>/g, ">");
            html += `<div class="checkbox-option"><label><input type="checkbox" name="${fieldName}" value="${opt}" ${
              isChecked ? "checked" : ""
            } ${disabledAttr}> <span class="checkbox-label">${escapedOpt}</span></label></div>`;
          });
          html += `</div>`;
          html += `<input type="hidden" name="${fieldName}_marker" value="1"></input>`;
        } else if (question.type === "fill-blanks") {
          html += renderFillBlanksInput(
            question.text,
            fieldName,
            fieldValue,
            isLocked,
            question.required
          );
        } else if (question.type === "inline-inputs") {
          const savedAnswers = fieldValue ? String(fieldValue).split("|") : [];
          html += `<div class="inline-inputs-container">`;
          question.labels.forEach((label, index) => {
            const value = savedAnswers[index] || "";
            const escapedValue = value.replace(/"/g, '"');
            html += `
              <div class="inline-input-group">
                <label for="${fieldName}_inline_${index}">${label}:</label>
                <input type="text" id="${fieldName}_inline_${index}" name="${fieldName}_inline_${index}" value="${escapedValue}" ${requiredAttr} ${readonlyStyle}>
              </div>
            `;
          });
          html += `</div>`;
        }

        html += `</div>`;
      }
    });

    html += `</div>`;
    surveyForm.innerHTML = html;

    const radiosQ0_2 = document.querySelectorAll('input[name="q0_2"]');
    if (radiosQ0_2.length > 0) {
      radiosQ0_2.forEach((radio) => {
        radio.addEventListener("change", handleTeachingStudentChange);
      });
      const checkedRadioQ0_2 = document.querySelector(
        'input[name="q0_2"]:checked'
      );
      if (checkedRadioQ0_2) {
        handleTeachingStudentChange({ target: checkedRadioQ0_2 });
      } else {
        const conditionalContainer = document.querySelector(
          ".conditional-fields-container"
        );
        if (conditionalContainer) {
          conditionalContainer
            .querySelectorAll(".conditional-field")
            .forEach((f) => (f.style.display = "none"));
        }
      }
    }
  } else {
    console.warn(`Invalid sectionIndex requested: ${sectionIndex}. Resetting.`);
    currentSection = attemptNumber === 1 ? -2 : 0;
    renderSection(currentSection);
    return;
  }

  setTimeout(() => {
    console.log(
      "Updating navigation buttons after rendering section",
      sectionIndex
    );
    updateNavigationButtons();
  }, 0);

  // Call our new function to set up the single-choice logic
  setupSingleChoiceCheckboxes();
}

function handlePreSurveyChange(radio) {
  const selectedValue = radio.value;
  const groupQuestionDiv = document.getElementById("q-2_1-container");

  if (groupQuestionDiv) {
    const shouldShow = selectedValue === "Ja";
    groupQuestionDiv.style.display = shouldShow ? "block" : "none";

    const inputs = groupQuestionDiv.querySelectorAll("input");
    inputs.forEach((input) => {
      input.required = shouldShow;
    });

    if (shouldShow) {
      const currentSelection = groupQuestionDiv.querySelector("input:checked");
      if (!currentSelection) {
        const savedGroup =
          userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";
        const savedInput = groupQuestionDiv.querySelector(
          `input[value="${savedGroup}"]`
        );
        if (savedInput) savedInput.checked = true;
      }
    }
  } else {
    console.log("Group question div NOT found!");
  }
}

/**
 * Handles changes to the 'Studieren Sie Lehramt?' radio buttons (q0_2).
 * Shows/hides conditional fields for Lehramt details or other studies.
 * Repopulates conditional fields based on saved data when shown.
 * @param {Event|HTMLInputElement} eventOrElement - Either the browser change event or the radio input element itself.
 */
function handleTeachingStudentChange(eventOrElement) {
  // --- Determine the actual radio button element ---
  // Check if it's an Event object (from user interaction) or the element directly (from programmatic call)
  const radio =
    eventOrElement.target instanceof Element
      ? eventOrElement.target
      : eventOrElement;

  // --- Add safety check ---
  if (!(radio instanceof HTMLInputElement) || radio.type !== "radio") {
    console.error(
      "handleTeachingStudentChange: Expected an Event or Radio Input Element, got:",
      eventOrElement
    );
    return; // Exit if it's not the expected type
  }
  // --- End safety check ---

  console.log(
    "handleTeachingStudentChange running for:",
    radio.name,
    "Value:",
    radio.value
  ); // Debug log

  const radioGroup = radio.closest(".radio-group");
  if (!radioGroup) {
    console.warn("No .radio-group found for", radio.name);
    return; // Exit if structure is unexpected
  }

  const selectedValue = radio.value;
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const conditionalFieldsContainer = radioGroup
    .closest(".question")
    ?.querySelector(".conditional-fields-container"); // Use optional chaining

  if (conditionalFieldsContainer) {
    const conditionalFields =
      conditionalFieldsContainer.querySelectorAll(".conditional-field");
    conditionalFields.forEach((field) => {
      const condition = field.dataset.condition;
      const shouldShow = selectedValue === condition;
      field.style.display = shouldShow ? "block" : "none";

      // Update required status and repopulate ONLY when showing the fields
      field.querySelectorAll("input, select").forEach((el) => {
        const fieldName = el.name;
        const isLocked = isFieldLocked(attemptNumber, 0, fieldName); // Section 0 for personal info

        if (shouldShow) {
          // Set required status based on attempt number ONLY when shown
          el.required = !isLocked && attemptNumber === 1;

          // Repopulate value from userData if not locked
          if (!isLocked) {
            // Get the appropriate value from userData
            let valueToSet = "";
            if (
              fieldName === "q0_6" ||
              fieldName === "q0_7" ||
              fieldName === "q0_8"
            ) {
              valueToSet = getPersonalDataValue(
                userData,
                attemptNumber,
                fieldName
              );
            }
            // Set value for select or input
            if (el.tagName.toLowerCase() === "select") {
              el.value = valueToSet || ""; // Set select value, default to empty if null/undefined
            } else {
              el.value = valueToSet || ""; // Set input value
            }
            console.log(
              `Repopulated conditional field ${fieldName} with value: ${valueToSet}`
            );
          } else {
            // If locked, ensure the value displayed matches the locked state (already handled by renderSection, but good practice)
            let lockedValue = getPersonalDataValue(
              userData,
              attemptNumber,
              fieldName
            );
            if (el.tagName.toLowerCase() === "select") {
              el.value = lockedValue || "";
            } else {
              el.value = lockedValue || "";
            }
          }
          // Ensure correct disabled/readonly state based on lock status
          el.disabled = isLocked && el.tagName.toLowerCase() === "select";
          el.readOnly = isLocked && el.tagName.toLowerCase() === "input";
          el.style.backgroundColor = isLocked ? "#eee" : "";
        } else {
          // If hidden, remove required attribute and clear value (unless locked)
          el.required = false;
          if (!isLocked) {
            if (el.tagName.toLowerCase() === "select") {
              el.value = ""; // Reset select
            } else {
              el.value = ""; // Reset input
            }
          }
          // Ensure hidden fields are not marked invalid visually
          el.setCustomValidity("");
          el.style.borderColor = "";
          const qDiv = el.closest(".question");
          if (qDiv) qDiv.classList.remove("unanswered");
        }
      });
    });
  } else {
    console.warn("Conditional fields container not found for q0_2.");
  }
}

function validateNumberInput(input) {
  let value = input.value.trim();
  value = value.replace(",", ".");

  if (value === "" && input.required) {
    input.setCustomValidity("Bitte geben Sie einen Wert ein.");
    input.style.borderColor = "red";
    return;
  }

  if (isNaN(value) && value !== "") {
    input.setCustomValidity("Bitte geben Sie eine gültige Zahl ein.");
    input.style.borderColor = "red";
    return;
  }

  if (!isNaN(value) && value !== "") {
    let numValue = parseFloat(value);
    const min = input.hasAttribute("min") ? parseFloat(input.min) : null;
    const max = input.hasAttribute("max") ? parseFloat(input.max) : null;

    if (min !== null && numValue < min) {
      input.setCustomValidity(
        `Bitte geben Sie einen Wert größer oder gleich ${min} ein.`
      );
      input.style.borderColor = "red";
      numValue = min;
    } else if (max !== null && numValue > max) {
      input.setCustomValidity(
        `Bitte geben Sie einen Wert kleiner oder gleich ${max} ein.`
      );
      input.style.borderColor = "red";
      numValue = max;
    } else {
      input.setCustomValidity("");
      input.style.borderColor = "";
    }

    input.value = numValue.toString();
  }
}

function markUnansweredQuestions() {
  const form = document.getElementById("surveyForm");
  if (!form) return null;

  const requiredFields = form.querySelectorAll("[required]");
  let firstInvalid = null;

  requiredFields.forEach((field) => {
    const questionDiv = field.closest(".question") || field.parentElement;
    if (!questionDiv) return; // Skip if no parent element

    // Check for visibility *before* checking if it's answered
    if (field.offsetParent !== null) {
      if (
        (field.type === "radio" &&
          !form.querySelector(`[name="${field.name}"]:checked`)) ||
        (field.type === "checkbox" && !field.checked) ||
        (field.type !== "radio" &&
          field.type !== "checkbox" &&
          !field.value.trim())
      ) {
        questionDiv.classList.add("unanswered");
        questionDiv.style.animation =
          "shake 0.82s cubic-bezier(.36,.07,.19,.97) both";
        if (!firstInvalid) firstInvalid = questionDiv;
      } else {
        questionDiv.classList.remove("unanswered");
        questionDiv.style.animation = "";
      }
    } else {
      // If not visible, remove any 'unanswered' class
      questionDiv.classList.remove("unanswered");
      questionDiv.style.animation = "";
    }
  });

  if (firstInvalid) {
    firstInvalid.scrollIntoView({ behavior: "smooth", block: "center" });
  }
  return firstInvalid;
}

function removeUnansweredMarkers() {
  document.querySelectorAll(".question.unanswered").forEach((q) => {
    q.classList.remove("unanswered");
  });
}

document.addEventListener("DOMContentLoaded", async function () {
  if (!window.location.pathname.includes("results.html")) {
    const attemptNumber = parseInt(
      sessionStorage.getItem("attemptNumber") || "1",
      10
    );
    // For T2 and T3, skip directly to personal information section
    if (attemptNumber > 1) {
      currentSection = 0;
    }
    await loadUserData();
    setupEventListeners();
  }
});

window.onload = function () {
  // *** KEY CHANGE: Check URL before calling loadUserData or showResults ***
  if (window.location.pathname.includes("results.html")) {
    //If it is result page we call showResults()
    const completed = localStorage.getItem("surveyCompleted") === "true";
    const userId = sessionStorage.getItem("userId");

    if (completed && userId) {
      const surveyForm = document.getElementById("surveyForm");
      if (surveyForm) surveyForm.innerHTML = ""; // Clear the form
      fetch(`php/get_user_data.php?userId=${userId}`) // Use the new PHP endpoint
        .then((res) => {
          if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
          return res.json();
        })
        .then((data) => {
          if (!data) throw new Error("No data received");
          userData = data;
          showResults();
        })
        .catch((err) => {
          console.error("Error loading completed survey:", err);
          sessionStorage.removeItem("surveyCompleted");
          currentSection = -1;
          // initializeSections();  // This function doesn't exist, remove it.
          // If we are on result page and error happens we go back to login page
          window.location.href = "login.html";
        });
    }
  } else {
    //If it is not result page we call loadUserData()
    const isNewAttempt = sessionStorage.getItem("startNewAttempt") === "true";
    const attemptNumber = parseInt(
      sessionStorage.getItem("attemptNumber") || "1"
    );

    if (isNewAttempt) {
      userData = null; // Clear previous user data
      currentSection = -1;
    }
    loadUserData();
  }
};

if (typeof surveyData === "undefined") {
  console.error(
    "surveyData ist nicht definiert. Bitte stellen Sie sicher, dass es geladen ist, bevor dieses Skript verwendet wird."
  );
}

const styleSheet = document.createElement("style");
styleSheet.textContent = `
  .radio-option input[type="radio"]:disabled:checked + .radio-checkmark {
    background-color: #0066cc;
    border-color: #0066cc;
  }
  .radio-option input[type="radio"]:disabled + .radio-checkmark {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;
document.head.appendChild(styleSheet);

function handleScaleClick(e) {
  const radioInput = e.target.previousElementSibling;
  if (radioInput && radioInput.type === "radio") {
    radioInput.checked = true;
    radioInput.dispatchEvent(new Event("change")); // Trigger change event
  }
}

function handleScaleKeydown(e) {
  if (e.key === " " || e.key === "Enter") {
    e.preventDefault();
    const radioInput = e.target.previousElementSibling;
    if (radioInput && radioInput.type === "radio") {
      radioInput.checked = true;
      radioInput.dispatchEvent(new Event("change")); // Trigger change event
    }
    e.target.setAttribute("aria-checked", "true");
  }
}

function createCompetencyChart1(initial, updated, followUp = {}) {
  const canvas = document.getElementById("competencyChart1");
  const descriptionBox = document.getElementById("descriptionBox1");
  if (!canvas || !descriptionBox) {
    console.error("Chart canvas or description box not found");
    return;
  }

  // Destroy existing chart instance if it exists
  if (window.competencyChart1 instanceof Chart) {
    window.competencyChart1.destroy();
  }

  const ctx = canvas.getContext("2d");

  // Combine categories from all datasets
  const allCategories = new Set([
    ...Object.keys(initial),
    ...Object.keys(updated),
    ...Object.keys(followUp),
  ]);
  allCategories.delete("overall");
  // Sort categories in the specified order using the sortCategories function from script.min.js
  const categories = sortCategories(Array.from(allCategories));

  const t1Data = categories.map((cat) => initial[cat] || 0);
  const t2Data = categories.map((cat) => updated[cat] || 0);
  const t3Data = categories.map((cat) => followUp[cat] || 0);

  // Retrieve userCode from sessionStorage or use 'Unknown' as default value if not found
  const userCode =
    userData.userCode ||
    sessionStorage.getItem("generatedCode") ||
    sessionStorage.getItem("resultsCode") ||
    "Unknown";
  // Pass userCode to createCompetencyChartConfig
  const chartConfig = createCompetencyChartConfig(
    categories,
    t1Data,
    t2Data,
    t3Data,
    "competencyChart1",
    "descriptionBox1",
    userCode || ""
  );
  window.competencyChart1 = new Chart(ctx, chartConfig);

  // Initial description box update
  if (categories.length > 0) {
    updateDescriptionBox(
      descriptionBox,
      categories[0],
      competencyDescriptions[categories[0]]
    );
  }
  descriptionBox.style.minHeight = "150px";
}

async function showResults() {
  const userId = parseInt(sessionStorage.getItem("userId"), 10);
  if (!userId) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Keine Benutzer-ID gefunden. Bitte erneut einloggen.",
    }).then(() => {
      window.location.href = "login.html";
    });
    return;
  }

  try {
    const res = await fetch(`php/get_user_data.php?userId=${userId}`);
    if (!res.ok) throw new Error("Failed to fetch user data");
    const data = await res.json();

    // Ensure userData is fully populated with latest data
    userData = {
      ...(userData || {}), // Keep existing data if any
      ...data, // Overwrite with fetched data
      responses: {
        t1: data.initialResponses || userData?.responses?.t1 || {},
        t2: data.updatedResponses || userData?.responses?.t2 || {},
        t3: data.followUpResponses || userData?.responses?.t3 || {},
      },
      scores: {
        t1: data.initialScores || userData?.scores?.t1 || {},
        t2: data.updatedScores || userData?.scores?.t2 || {},
        t3: data.followUpScores || userData?.scores?.t3 || {},
      },
      openEndedResponses: {
        ...(userData?.openEndedResponses || {}),
        ...data.openEndedResponses,
      },
      // Ensure preSurveyResponses are loaded correctly
      preSurveyResponses:
        data.preSurveyResponses || userData?.preSurveyResponses || {},
    };

    const initialScores = userData.scores?.t1 || {};
    const updatedScores = userData.scores?.t2 || {};
    const followUpScores = userData.scores?.t3 || {};

    const attemptNumber =
      parseInt(sessionStorage.getItem("attemptNumber"), 10) || 1;

    const finalScores =
      Object.keys(followUpScores).length > 0
        ? followUpScores
        : Object.keys(updatedScores).length > 0
        ? updatedScores
        : initialScores;

    const overallScore = (finalScores.overall || 0).toFixed(1); // Format score
    const t1Reflection = userData.openEndedResponses?.t1_strategy || ""; // Correct key for T1
    const t2Reflection = userData.openEndedResponses?.t2_reflection || "";
    const t3Reflection = userData.openEndedResponses?.t3_reflection || "";

    // Get the group from preSurveyResponses - ensure it's loaded
    const group = userData.preSurveyResponses?.["q-2_1"] || "Gruppe A"; // Default to A if not found

    console.log("Showing Results for Group:", group, "Attempt:", attemptNumber);
    console.log("Chart data:", {
      initialScores,
      updatedScores,
      followUpScores,
    });
    console.log("Reflections:", { t1Reflection, t2Reflection, t3Reflection });

    let introHtml = "";
    let chartHtml = ""; // Separate chart HTML
    let reflectionHtml = "";
    let finalMessageHtml = ""; // For final messages/links

    // --- Define HTML structure based on Group and Attempt ---

    // Common Chart Structure (used by A, C, and later B/D)
    const commonChartHtml = `
      <h3>Kompetenzdiagramm</h3>
      <p>Bewegen Sie den Mauszeiger über die Balken im Diagramm, um detaillierte Informationen zu den einzelnen Kompetenzbereichen zu erhalten.</p>
      <div class="chart-container" style="height: 350px; width: 100%; margin-bottom: 1rem;">
        <canvas id="competencyChart1"></canvas>
      </div>
      <div id="descriptionBox1" class="info-box"></div>
      <div class="button-container">
        <button id="downloadChart" class="btn btn-primary">
          <i class="fas fa-download"></i> Diagramm herunterladen
        </button>
      </div>
      <hr>
    `;

    // --- Group D Logic ---
    if (group === "Gruppe D") {
      // Hide the placeholder results section from HTML
      const resultsSectionPlaceholder =
        document.getElementById("results-section");
      if (resultsSectionPlaceholder)
        resultsSectionPlaceholder.style.display = "none";

      if (attemptNumber === 1) {
        introHtml = `
          <h3>Vielen Dank für die Teilnahme!</h3>
          <p>Sie haben im Folgenden eine Übersicht über die verschiedenen Kompetenzbereiche digitaler Kompetenz. Beantworten Sie im letzten Schritt bitte die Frage unter der Abbildung.</p>
        `;
        // Add competency overview for Group D T1
        chartHtml = `
          <div class="competency-overview">
            <h4>Übersicht der Kompetenzbereiche</h4>
            <div class="competency-grid">
              <div class="competency-item">
                <h5>Suchen, Verarbeiten und Aufbewahren</h5>
                <p>Informationen digital finden, bewerten und organisieren</p>
              </div>
              <div class="competency-item">
                <h5>Kommunikation und Kollaborieren</h5>
                <p>Digital kommunizieren und zusammenarbeiten</p>
              </div>
              <div class="competency-item">
                <h5>Produzieren und Präsentieren</h5>
                <p>Digitale Inhalte erstellen und teilen</p>
              </div>
              <div class="competency-item">
                <h5>Schützen und sicher Agieren</h5>
                <p>Sicherheit und Datenschutz im digitalen Raum</p>
              </div>
              <div class="competency-item">
                <h5>Problemlösen und Handeln</h5>
                <p>Technische Probleme lösen und digitale Werkzeuge nutzen</p>
              </div>
              <div class="competency-item">
                <h5>Analysieren und Reflektieren</h5>
                <p>Digitale Medien kritisch bewerten und reflektieren</p>
              </div>
            </div>
          </div>
        `;
        reflectionHtml = `
          <p><strong>Was sind Kompetenzbereiche, in denen Sie sich weiterbilden wollen würden? Die Auswahl der Kompetenzbereiche können Sie anhand verschiedener Motive selbst vornehmen: Würden Sie Ihre Fähigkeiten im Kompetenzbereich weiterentwickeln, in dem Sie sich wenig kompetent einschätzen oder interessieren Sie sich gerade besonders für einen Kompetenzbereich.</strong></p>
          <textarea id="t1OpenEndedResponse" rows="5" required>${t1Reflection}</textarea>
          <button id="submitT1OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        finalMessageHtml = `<p>Wir melden uns in ein paar Wochen nochmals und würden Sie darum bitten, nochmals einen Fragebogen auszufüllen. <a href="index.html">Hier gelangen Sie zurück zur Startseite.</a></p>`;
      } else if (attemptNumber === 2) {
        // --- THIS IS THE MODIFIED SECTION FOR GROUP D, T2 ---
        introHtml = `
          <h4>Schön, dass Sie nochmals an der Befragung teilgenommen haben.</h4>
          <p>Was glauben Sie, wie haben sich Ihre digitalen Kompetenzen im Verlauf des Forschungsprojektes verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche. Diese sehen Sie erneut unter dieser Aufgabe.</p>
        `;
        reflectionHtml = `
          <textarea id="t2OpenEndedResponse" rows="5" required>${t2Reflection}</textarea>
          <button id="submitT2OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Add competency overview grid for Group D T2
        chartHtml = `
          <div class="competency-overview">
            <h4>Übersicht der Kompetenzbereiche</h4>
            <div class="competency-grid">
              <div class="competency-item">
                <h5>Suchen, Verarbeiten und Aufbewahren</h5>
                <p>Informationen digital finden, bewerten und organisieren</p>
              </div>
              <div class="competency-item">
                <h5>Kommunikation und Kollaborieren</h5>
                <p>Digital kommunizieren und zusammenarbeiten</p>
              </div>
              <div class="competency-item">
                <h5>Produzieren und Präsentieren</h5>
                <p>Digitale Inhalte erstellen und teilen</p>
              </div>
              <div class="competency-item">
                <h5>Schützen und sicher Agieren</h5>
                <p>Sicherheit und Datenschutz im digitalen Raum</p>
              </div>
              <div class="competency-item">
                <h5>Problemlösen und Handeln</h5>
                <p>Technische Probleme lösen und digitale Werkzeuge nutzen</p>
              </div>
              <div class="competency-item">
                <h5>Analysieren und Reflektieren</h5>
                <p>Digitale Medien kritisch bewerten und reflektieren</p>
              </div>
            </div>
          </div>
        `;
        // Final message will be shown in Swal after submission
      } else if (attemptNumber === 3) {
        const isFirstStep = !sessionStorage.getItem("t3FirstStepCompleted_D"); // Use specific flag

        if (isFirstStep) {
          introHtml = `
            <h4>Schön, dass Sie nochmals an der Umfrage teilgenommen haben.</h4>
            <p>Was glauben Sie nun nach Abschluss des Forschungsprojektes, wie haben sich Ihre digitalen Kompetenzen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</p>
          `;
          reflectionHtml = `
            <textarea id="t3OpenEndedResponse" rows="5" required>${t3Reflection}</textarea>
            <button id="submitT3OpenEndedResponse" class="btn btn-primary">Absenden</button>
          `;
        } else {
          // Step 2: Show chart and additional reflection
          introHtml = `
            <h4>Ihre Kompetenzentwicklung</h4>
            <p>Wir möchten Ihnen im Folgenden Ihre Kompetenzentwicklung im Verlauf des Projektes grafisch darstellen. Sie sehen Ihre durchschnittlichen Einschätzungen für alle sechs Kompetenzbereiche in allen Befragungen, an denen Sie teilgenommen haben.</p>
          `;
          chartHtml = commonChartHtml; // Use the common chart structure
          reflectionHtml = `
            <p><strong>Abschließend würden wir gerne wissen, was Sie denken. Passen Ihre vorherigen Einschätzungen zu Ihrer Entwicklung zu den Ergebnissen aus der Grafik? Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
            <textarea id="t3AdditionalResponse" rows="5" required></textarea>
            <button id="submitT3AdditionalResponse" class="btn btn-primary">Absenden</button>
          `;
          // Final message with links shown in Swal after submission
        }
      }
    }
    // --- Group C Logic ---
    else if (group === "Gruppe C") {
      chartHtml = commonChartHtml; // Show chart in all attempts for C
      if (attemptNumber === 1) {
        introHtml = `
          <h4>Vielen Dank, dass Sie an der Umfrage teilgenommen haben.</h4>
          <p>Im Folgenden sehen Sie Ihren durchschnittlichen Score für die eingeschätzten digitalen Kompetenzen.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
        `;
        reflectionHtml = `
          <p><strong>Was sind Kompetenzbereiche, in denen Sie sich weiterbilden wollen würden? Die Auswahl der Kompetenzbereiche können Sie anhand verschiedener Motive selbst vornehmen: Würden Sie Ihre Fähigkeiten im Kompetenzbereich weiterentwickeln, in dem Sie sich wenig kompetent einschätzen oder interessieren Sie sich gerade besonders für einen Kompetenzbereich.</strong></p>
          <textarea id="t1OpenEndedResponse" rows="5" required>${t1Reflection}</textarea>
          <button id="submitT1OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        finalMessageHtml = `<p>Wir melden uns in ein paar Wochen nochmals für die nächste Befragung. Hier können Sie auf die <a href="index.html">Startseite</a> zurück gelangen.</p>`;
      } else if (attemptNumber === 2) {
        introHtml = `
          <h4>Willkommen zur zweiten Befragung! Schön, dass Sie erneut an unserer Befragung teilnehmen.</h4>
          <p>Im Folgenden sehen Sie wieder Ihren durchschnittlichen Score für die eingeschätzten digitalen Kompetenzen, nun aber für die zweite Befragung.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
          <p>Zudem haben wir wieder Ihre Ergebnisse für jeden der sechs Kompetenzbereiche aufgeschlüsselt, sodass Sie herausfinden können, in welchen Kompetenzbereichen Sie höhere oder niedrigere Werte haben und wie sich die Werte im Vergleich zur ersten Befragung verändert haben.</p>
        `;
        reflectionHtml = `
          <div class="info-box">
            <p>Jetzt sehen Sie Ihre Kompetenzeinschätzung im Vergleich zur ersten Befragung. Wenn der helle Balken (T1) niedriger ist als der dunkle Balken (T2), bedeutet das, dass Sie Ihre digitalen Kompetenzen heute höher einschätzt als zu Beginn des Projektes. Ist der helle Balken höher als der dunkle Balken, ist es genau umgekehrt. Es ist auch möglich, dass Sie sich bei beiden Befragungen in einzelnen Kompetenzbereichen gleich eingeschätzt haben, dann sind beide Balken gleich hoch.</p>
          </div>
          <p><strong>Wie haben sich Ihre digitalen Kompetenzen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
          <textarea id="t2OpenEndedResponse" rows="5" required>${t2Reflection}</textarea>
          <button id="submitT2OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Final message shown in Swal
      } else if (attemptNumber === 3) {
        // --- THIS IS THE CORRECTED SECTION FOR GROUP C, T3 ---
        introHtml = `
          <p>Wir möchten Ihnen im Folgenden Ihre Kompetenzentwicklung im Verlauf des Projektes grafisch darstellen. Sie sehen Ihre durchschnittlichen Einschätzungen für alle sechs Kompetenzbereiche in allen Befragungen, an denen Sie teilgenommen haben.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
        `;
        reflectionHtml = `
          <div class="info-box">
             <p>Jetzt sehen Sie den Vergleich Ihrer Kompetenzeinschätzungen über drei unterschiedliche Zeitpunkte (T1, T2, T3). Wenn der linke Balken (T1) niedriger ist als der mittlere (T2) oder rechte Balken (T3), bedeutet das, dass Sie Ihre Kompetenzen im Verlauf des Projektes höher einschätzen als zuvor. Ist der linke Balken höher als die anderen, ist es genau umgekehrt. Es ist auch möglich, dass Sie sich bei allen drei Befragungen in gewissen Kompetenzbereichen gleich eingeschätzt haben: dann sind alle drei Balken gleich hoch.</p>
          </div>
          <p><strong>Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
          <textarea id="t3OpenEndedResponse" rows="5" required>${t3Reflection}</textarea>
          <button id="submitT3OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Final message with links shown in Swal
      }
    }
    // --- Group B Logic ---
    else if (group === "Gruppe B") {
      // No chart initially for Group B
      if (attemptNumber === 1) {
        introHtml = `
          <h4>Wählen Sie nun einen oder mehrere Kompetenzbereiche aus, in denen Sie sich weiterbilden möchten.</h4>
          <p>Wir haben dafür für jeden Kompetenzbereich mehrere Mikrofortbildungen entwickelt. Die Auswahl der Kompetenzbereiche können Sie anhand verschiedener Motive selbst vornehmen: Möchten Sie Ihre Fähigkeiten in dem Kompetenzbereich weiterentwickeln, in dem Sie sich wenig kompetent einschätzen oder interessieren Sie sich gerade besonders für einen Kompetenzbereich.</p>

          <div class="competency-areas-overview">
            <h3>Übersicht der Kompetenzbereiche:</h3>
            <div class="competency-area"><h4>1. Suchen, Verarbeiten und Aufbewahren</h4><p>Gezielt nach digitalen Informationen suchen, deren Glaubwürdigkeit bewerten, Daten effektiv organisieren und sicher speichern/abrufen.</p></div>
            <div class="competency-area"><h4>2. Kommunikation und Kollaborieren</h4><p>Effektiv über digitale Kanäle interagieren und zusammenarbeiten, Inhalte angemessen teilen und an Online-Gemeinschaften teilhaben.</p></div>
            <div class="competency-area"><h4>3. Produzieren und Präsentieren</h4><p>Digitale Inhalte (z.B. Texte, Bilder, Videos) erstellen, bearbeiten und kombinieren sowie ansprechend präsentieren.</p></div>
            <div class="competency-area"><h4>4. Schützen und sicher Agieren</h4><p>Die eigene digitale Identität, Daten und Geräte schützen, Online-Risiken managen und auf digitales Wohlbefinden achten.</p></div>
            <div class="competency-area"><h4>5. Problemlösen und Handeln</h4><p>Technische Schwierigkeiten identifizieren und beheben, digitale Werkzeuge für spezifische Aufgaben auswählen und anpassen.</p></div>
            <div class="competency-area"><h4>6. Analysieren und Reflektieren</h4><p>Die Funktionsweise und Wirkung digitaler Medien verstehen, Informationen kritisch hinterfragen und den Einfluss auf sich selbst und die Gesellschaft reflektieren.</p></div>
          </div>

          <p><strong>Benennen Sie unten im Feld ein oder zwei Kompetenzbereiche, in denen Sie Mikrofortbildungen absolvieren möchten und begründen Sie Ihre Entscheidung.</strong></p>
        `;
        reflectionHtml = `
          <textarea id="t1OpenEndedResponse" rows="5" required>${t1Reflection}</textarea>
          <button id="submitT1OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Course links shown after submission
      } else if (attemptNumber === 2) {
        introHtml = `
          <h4>Schön, dass Sie nochmals an der Befragung teilgenommen haben.</h4>
          <p>Was glauben Sie nun nach Absolvierung der Kurse, wie haben sich Ihre digitalen Kompetenzen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</p>
          <p>Diese sehen Sie erneut unter dieser Aufgabe.</p>
        `;
        reflectionHtml = `
          <textarea id="t2OpenEndedResponse" rows="5" required>${t2Reflection}</textarea>
          <button id="submitT2OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        chartHtml = `
          <div class="competency-overview">
            <h4>Übersicht der Kompetenzbereiche</h4>
            <div class="competency-grid">
              <div class="competency-item">
                <h5>Suchen, Verarbeiten und Aufbewahren</h5>
                <p>Informationen digital finden, bewerten und organisieren</p>
              </div>
              <div class="competency-item">
                <h5>Kommunikation und Kollaborieren</h5>
                <p>Digital kommunizieren und zusammenarbeiten</p>
              </div>
              <div class="competency-item">
                <h5>Produzieren und Präsentieren</h5>
                <p>Digitale Inhalte erstellen und teilen</p>
              </div>
              <div class="competency-item">
                <h5>Schützen und sicher Agieren</h5>
                <p>Sicherheit und Datenschutz im digitalen Raum</p>
              </div>
              <div class="competency-item">
                <h5>Problemlösen und Handeln</h5>
                <p>Technische Probleme lösen und digitale Werkzeuge nutzen</p>
              </div>
              <div class="competency-item">
                <h5>Analysieren und Reflektieren</h5>
                <p>Digitale Medien kritisch bewerten und reflektieren</p>
              </div>
            </div>
          </div>
        `;
        // Final message shown in Swal
      } else if (attemptNumber === 3) {
        const isFirstStepCompleted =
          sessionStorage.getItem("t3FirstStepCompleted_B") === "true";

        if (!isFirstStepCompleted) {
          introHtml = `
            <p>Was glauben Sie nun nach Absolvierung der Kurse, wie haben sich Ihre digitalen Kompetenzen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</p>
          `;
          chartHtml = "";
          reflectionHtml = `
                  <div class="question">
                     <textarea id="t3OpenEndedResponse" rows="5" required>${t3Reflection}</textarea>
                  </div>
                  <button id="submitT3OpenEndedResponse" class="btn btn-primary">Absenden</button>
                `;
          finalMessageHtml = "";
        } else {
          introHtml = `
                  <h4>Ihre Kompetenzentwicklung</h4>
                  <p>Nachdem Sie die Kurse absolviert haben, möchten wir Ihnen nun im Folgenden Ihre Kompetenzentwicklung im Verlauf des Projektes grafisch darstellen. Sie sehen Ihre durchschnittlichen Einschätzungen für alle sechs Kompetenzbereiche in allen Befragungen, an denen Sie teilgenommen haben.</p>
                `;
          chartHtml = commonChartHtml;
          const t3AdditionalReflection =
            userData?.openEndedResponses?.t3_additional_reflection || "";
          reflectionHtml = `
                  <p><strong>Abschließend würden wir gerne wissen, was Sie denken. Passen Ihre vorherigen Einschätzungen zu Ihrer Entwicklung zu den Ergebnissen aus der Grafik? Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
                  <div class="question">
                     <textarea id="t3AdditionalResponse" rows="5" required>${t3AdditionalReflection}</textarea>
                  </div>
                  <button id="submitT3AdditionalResponse" class="btn btn-primary">Absenden</button>
                `;
          finalMessageHtml = "";
        }
      }
    }
    // --- Group A Logic (Default) ---
    else {
      chartHtml = commonChartHtml; // Show chart in all attempts for A
      if (attemptNumber === 1) {
        introHtml = `
          <h4>Vielen Dank, dass Sie an der Umfrage teilgenommen haben.</h4>
          <p>Im Folgenden sehen Sie Ihren durchschnittlichen Score für die eingeschätzten digitalen Kompetenzen.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
          <p>Zudem haben wir die Ergebnisse für jeden der sechs Kompetenzbereiche aufgeschlüsselt, sodass Sie herausfinden können, in welchen Kompetenzbereichen Sie höhere oder niedrigere Werte haben.</p>
        `;
        reflectionHtml = `
            <div class="info-box">
              <p>Basierend auf den Ergebnissen aus der Befragung können Sie nun einen oder mehrere Kompetenzbereiche auswählen, in denen Sie sich weiterbilden möchten. Wir haben für jeden Kompetenzbereich mehrere Mikrofortbildungen entwickelt, die Sie bearbeiten können. Die Auswahl der Kompetenzbereiche können Sie anhand verschiedener Strategien vornehmen: Möchten Sie beispielsweise den Kompetenzbereich mit den geringsten Ausprägungen verbessern oder interessieren Sie sich gerade besonders für einen Kompetenzbereich?</p>
            </div>
            <p><strong>Schauen Sie sich nun Ihre Ergebnisse an, benennen Sie unten im Feld ein oder zwei Kompetenzbereiche, in denen Sie Mikrofortbildungen absolvieren möchten und begründen Sie Ihre Entscheidung.</strong></p>
            <textarea id="t1OpenEndedResponse" rows="5" required>${t1Reflection}</textarea>
            <button id="submitT1OpenEndedResponse" class="btn btn-primary">Absenden</button>
          `;
      } else if (attemptNumber === 2) {
        introHtml = `
          <h4>Schön, dass Sie verschiedene Kurse absolviert haben und nochmals an der Umfrage teilgenommen haben.</h4>
          <p>Im Folgenden sehen Sie wieder Ihren durchschnittlichen Score für die eingeschätzten digitalen Kompetenzen, nun aber für die zweite Befragung.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
          <p>Zudem haben wir wieder Ihre Ergebnisse für jeden der sechs Kompetenzbereiche aufgeschlüsselt, sodass Sie herausfinden können, in welchen Kompetenzbereichen Sie höhere oder niedrigere Werte haben und wie sich die Werte im Vergleich zur ersten Befragung verändert haben.</p>
        `;
        reflectionHtml = `
          <div class="info-box">
            <p>Jetzt haben Sie den Vergleich zwischen Ihrer Kompetenzeinschätzung vor (T1) und nach (T2) der Absolvierung der ILIAS Kurse. Wenn der helle Balken (T1) niedriger ist als der dunkle Balken (T2), bedeutet das, dass Sie Ihre digitalen Kompetenzen nach der Bearbeitung der ILIAS-Kursen höher einschätzt als zuvor. Ist der helle Balken höher als der dunkle Balken, ist es genau umgekehrt.</p>
          </div>
          <p><strong>Wie haben sich Ihre digitalen Kompetenzen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
          <textarea id="t2OpenEndedResponse" rows="5" required>${t2Reflection}</textarea>
          <button id="submitT2OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Final message shown in Swal
      } else if (attemptNumber === 3) {
        introHtml = `
          <h4>Schön, dass Sie nochmals an der Umfrage teilgenommen haben.</h4>
          <p>Im Folgenden sehen Sie wieder Ihren durchschnittlichen Score für die eingeschätzten digitalen Kompetenzen, nun aber für die abschließende Befragung.</p>
          <p class="score-display"><strong>Ihr Kompetenzscore beträgt ${overallScore}%</strong></p>
          <p>Zudem haben wir wieder Ihre Ergebnisse für jeden der sechs Kompetenzbereiche aufgeschlüsselt, sodass Sie herausfinden können, in welchen Kompetenzbereichen Sie höhere oder niedrigere Werte haben und wie sich die Werte im Vergleich zur ersten und zweiten Befragung verändert haben.</p>
        `;
        reflectionHtml = `
          <div class="info-box">
            <p>Jetzt sehen Sie den Vergleich Ihrer Kompetenzeinschätzungen über drei unterschiedliche Zeitpunkte (T1, T2, T3). Wenn der linke Balken (T1) niedriger ist als der mittlere (T2) oder rechte Balken (T3), bedeutet das, dass Sie Ihre Kompetenzen nach der Bearbeitung der ILIAS-Kurse höher eingeschätzt als zuvor. Ist der linke Balken höher als die anderen, ist es genau umgekehrt.</p>
          </div>
          <p><strong>Wie haben sich Ihre digitalen Kompetenzen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.</strong></p>
          <textarea id="t3OpenEndedResponse" rows="5" required>${t3Reflection}</textarea>
          <button id="submitT3OpenEndedResponse" class="btn btn-primary">Absenden</button>
        `;
        // Final message shown in Swal
      }
    }

    // --- Assemble the final HTML and inject it ---
    let finalHtml;
    if ((group === "Gruppe D" || group === "Gruppe B") && attemptNumber === 2) {
      // Special order for Group D and B at T2
      finalHtml = `
      <div class="results-section">
        ${introHtml}
        ${reflectionHtml}
        ${chartHtml}
        ${finalMessageHtml}
      </div>
    `;
    } else {
      // Default order for everyone else
      finalHtml = `
      <div class="results-section">
        ${introHtml}
        ${chartHtml}
        ${reflectionHtml}
        ${finalMessageHtml}
      </div>
    `;
    }

    const surveyFormContainer = document.getElementById("surveyForm");
    if (surveyFormContainer) {
      surveyFormContainer.innerHTML = finalHtml;
      surveyFormContainer.style.display = "block";
    } else {
      console.error("Container #surveyForm not found!");
    }

    const resultsSectionPlaceholder =
      document.getElementById("results-section");
    if (resultsSectionPlaceholder)
      resultsSectionPlaceholder.style.display = "none";

    window.scrollTo({ top: 0, behavior: "smooth" });

    const progressBar = document.getElementById("progressBar");
    const progressText = document.getElementById("progressText");
    if (progressBar) progressBar.parentElement.style.display = "none";
    if (progressText) progressText.style.display = "none";

    if (chartHtml) {
      if (Object.keys(initialScores).length > 0) {
        createCompetencyChart1(initialScores, updatedScores, followUpScores);
        const downloadButton = document.getElementById("downloadChart");
        if (downloadButton) {
          downloadButton.addEventListener("click", function (e) {
            e.preventDefault();
            if (!sessionStorage.getItem("userId")) {
              Swal.fire("Fehler", "Bitte melden Sie sich erneut an.", "error");
              return;
            }
            downloadChart();
          });
        }
      } else {
        console.warn("Initial scores not available, cannot create chart.");
        const chartCanvas = document.getElementById("competencyChart1");
        if (chartCanvas && chartCanvas.parentElement) {
          chartCanvas.parentElement.innerHTML =
            "<p><em>Diagrammdaten sind nicht verfügbar.</em></p>";
        }
      }
    }

    document
      .getElementById("submitT1OpenEndedResponse")
      ?.addEventListener("click", submitT1OpenEndedResponse);
    document
      .getElementById("submitT2OpenEndedResponse")
      ?.addEventListener("click", submitT2OpenEndedResponse);
    document
      .getElementById("submitT3OpenEndedResponse")
      ?.addEventListener("click", submitT3OpenEndedResponse);
    document
      .getElementById("submitT3AdditionalResponse")
      ?.addEventListener("click", (e) => {
        if (group === "Gruppe D") {
          submitT3AdditionalResponseD(e);
        } else {
          submitT3AdditionalResponse(e);
        }
      });

    if (group === "Gruppe C" && attemptNumber === 3) {
      setTimeout(
        () => confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } }),
        500
      );
    }
    if (
      group === "Gruppe D" &&
      attemptNumber === 3 &&
      sessionStorage.getItem("t3FirstStepCompleted_D")
    ) {
      setTimeout(
        () => confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } }),
        500
      );
    }

    hideNavigationButtons();
  } catch (error) {
    console.error("Error displaying results:", error);
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Es gab einen Fehler beim Anzeigen der Ergebnisse. Bitte versuchen Sie es erneut.",
    });
    document.getElementById("surveyForm").innerHTML =
      '<p class="error-message">Ergebnisse konnten nicht geladen werden. Bitte versuchen Sie, sich neu anzumelden.</p>';
    hideNavigationButtons();
  }
}

function submitT3AdditionalResponseD(event) {
  event.preventDefault();
  const responseElement = document.getElementById("t3AdditionalResponse");
  if (!responseElement) return;
  const response = responseElement.value.trim();
  if (!response) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte füllen Sie das Textfeld aus.",
    });
    return;
  }

  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t3_additional_reflection",
      response,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then(() => {
      responseElement.disabled = true;
      const submitButton = document.getElementById(
        "submitT3AdditionalResponse"
      );
      if (submitButton) submitButton.disabled = true;

      // Show final message with course links for Group D
      Swal.fire({
        icon: "success",
        title: "Gespeichert",
        html: `
          <p>Danke für Ihre Teilnahme. Im Rahmen des Projekts <strong>Open-Digi</strong> haben wir Mikrofortbildungen erstellt, die Ihnen dabei helfen, Ihre digitalen Kompetenzen gezielt zu fördern. Wenn Sie Lust haben, sich in bestimmten Kompetenzbereichen weiterzuentwickeln, dann absolvieren Sie gerne unsere Mikrofortbildungen.</p>
          ${getCourseLinksHtml()}
          <p>Hier können Sie auf die <a href="index.html">Startseite</a> zurück gelangen.</p>
        `,
        confirmButtonText: "OK",
        didOpen: () =>
          confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } }),
      });
      // Clear the flag so if they refresh they don't get stuck
      sessionStorage.removeItem("t3FirstStepCompleted_D");
    })
    .catch((error) => {
      console.error("Error:", error);
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer Antwort.",
      });
    });
}

function submitT2OpenEndedResponse(event) {
  event.preventDefault();
  const responseElement = document.getElementById("t2OpenEndedResponse");
  if (!responseElement) return;
  const response = responseElement.value.trim();
  if (!response) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte füllen Sie das Textfeld aus.",
    });
    return;
  }
  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group = userData.preSurveyResponses?.["q-2_1"] || "Gruppe A";

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t2_reflection",
      response,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then(() => {
      responseElement.disabled = true;
      const submitButton = document.getElementById("submitT2OpenEndedResponse");
      if (submitButton) submitButton.disabled = true;

      // Common final message for T2 for all groups (A, B, C, D)
      Swal.fire({
        icon: "success",
        title: "Gespeichert",
        html: 'Danke für Ihre Teilnahme. Wir melden uns in etwa einem Monat nochmal, damit Sie an der dritten Befragung teilnehmen können. Hier können Sie auf die <a href="index.html">Startseite</a> zurück gelangen.',
        confirmButtonText: "OK",
        didOpen: () =>
          confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } }),
      });
    })
    .catch((error) => {
      console.error("Error:", error);
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer Antwort.",
      });
    });
}

function submitT3OpenEndedResponse(event) {
  event.preventDefault();
  const responseElement = document.getElementById("t3OpenEndedResponse");
  if (!responseElement) {
    console.error("T3 reflection textarea not found!");
    return;
  }
  const response = responseElement.value.trim();

  if (!response) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte füllen Sie das Textfeld aus.",
    });
    return;
  }
  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group = userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";

  console.log(
    `Submitting T3 initial reflection for Group ${group}. Response: "${response}"`
  );

  responseElement.disabled = true;
  const submitButtonElement = document.getElementById(
    "submitT3OpenEndedResponse"
  );
  if (submitButtonElement) {
    submitButtonElement.disabled = true;
    submitButtonElement.textContent = "Wird gespeichert...";
  }

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t3_reflection",
      response,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then((saveData) => {
      console.log("T3 initial reflection saved:", saveData);
      if (userData && userData.openEndedResponses) {
        userData.openEndedResponses["t3_reflection"] = response;
      }

      // --- Group-Specific Logic for next step ---
      if (group === "Gruppe B") {
        console.log(
          "Group B: T3 initial reflection submitted. Setting flag and calling showResults for chart step."
        );
        sessionStorage.setItem("t3FirstStepCompleted_B", "true");
        showResults();
      } else if (group === "Gruppe D") {
        console.log(
          "Group D: T3 initial reflection submitted. Setting flag and calling showResults for chart step."
        );
        sessionStorage.setItem("t3FirstStepCompleted_D", "true");
        showResults();
      } else if (group === "Gruppe A") {
        // MODIFIED: Only Group A gets email prompt here
        console.log(
          "Group A: T3 initial reflection submitted. Preparing to show email prompt."
        );
        const emailValue = userData?.openEndedResponses?.t3_email || "";
        const surveyForm = document.getElementById("surveyForm");
        const resultsSection = surveyForm.querySelector(".results-section");

        if (resultsSection) {
          console.log(
            "Group A: Found .results-section. Appending email prompt."
          );

          const emailContainer = document.createElement("div");
          emailContainer.className = "final-inputs";

          const paragraph = document.createElement("p");
          paragraph.innerHTML =
            "<strong>Sie sind nun am Ende des Forschungsprojektes angekommen. Wenn Sie Interesse an einem weiteren Austausch zu ihrer Lernerfahrung haben, tragen Sie im Folgenden bitte Ihre E-Mail Adresse ein und wir werden uns bei Ihnen melden.</strong>";
          emailContainer.appendChild(paragraph);

          const inputGroup = document.createElement("div");
          inputGroup.className = "email-input-group";

          const emailInput = document.createElement("input");
          emailInput.type = "email";
          emailInput.id = "t3EmailResponse";
          emailInput.value = emailValue;
          emailInput.placeholder = "Ihre E-Mail-Adresse (optional)";
          inputGroup.appendChild(emailInput);

          const emailSubmitBtn = document.createElement("button");
          emailSubmitBtn.id = "submitT3EmailResponse";
          emailSubmitBtn.className = "btn btn-primary";
          emailSubmitBtn.textContent = "Absenden";
          emailSubmitBtn.addEventListener("click", submitT3EmailResponse);
          console.log(
            "Group A: Event listener added directly to email submit button."
          );
          inputGroup.appendChild(emailSubmitBtn);

          emailContainer.appendChild(inputGroup);
          resultsSection.appendChild(emailContainer);
          console.log("Group A: Email prompt HTML appended.");

          if (typeof emailContainer.scrollIntoView === "function") {
            emailContainer.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
            console.log("Group A: Scrolled to new email prompt.");
          }
        } else {
          console.error(
            "Group A: .results-section not found. Cannot append email prompt."
          );
        }
      } else if (group === "Gruppe C") {
        // ADDED: Specific handling for Group C
        console.log(
          "Group C: T3 initial reflection submitted. Showing final message with course links."
        );
        Swal.fire({
          icon: "success",
          title: "Vielen Dank!",
          html: `Danke für Ihre Teilnahme. Im Rahmen des Projekts <strong>Open-Digi</strong> haben wir Mikrofortbildungen erstellt, die Ihnen dabei helfen, Ihre digitalen Kompetenzen gezielt zu fördern. Wenn Sie Lust haben, sich in bestimmten Kompetenzbereichen weiterzuentwickeln, dann absolvieren Sie gerne unsere Mikrofortbildungen.<br><br>
          ${getCourseLinksHtml()}<br>
          Hier können Sie auf die <a href="index.html">Startseite</a> zurück gelangen.`,
          confirmButtonText: "OK",
          didOpen: () => {
            if (typeof confetti === "function")
              confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
          },
        });
        // Clear any T3 step flags (though Group C doesn't use them, good for consistency)
        sessionStorage.removeItem("t3FirstStepCompleted_B");
        sessionStorage.removeItem("t3FirstStepCompleted_D");
      } else {
        console.warn(
          "T3 initial reflection: Unhandled group for next step:",
          group
        );
        Swal.fire({
          icon: "success",
          title: "Gespeichert",
          html: 'Danke für Ihre Teilnahme. Hier gelangen Sie zurück zur <a href="index.html">Startseite</a>.',
          confirmButtonText: "OK",
          didOpen: () => {
            if (typeof confetti === "function")
              confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
          },
        });
      }
    })
    .catch((error) => {
      console.error("Error saving T3 initial reflection:", error);
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer Antwort.",
      });
      responseElement.disabled = false;
      if (submitButtonElement) {
        submitButtonElement.disabled = false;
        submitButtonElement.textContent = "Absenden";
      }
    });
}

function submitT1OpenEndedResponse(event) {
  event.preventDefault();
  const responseElement = document.getElementById("t1OpenEndedResponse");
  if (!responseElement) return;
  const response = responseElement.value.trim();
  if (!response) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte füllen Sie das Textfeld aus.",
    });
    return;
  }
  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t1_strategy",
      response,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then(() => {
      responseElement.disabled = true;
      const submitButton = document.getElementById("submitT1OpenEndedResponse");
      if (submitButton) submitButton.disabled = true;
      // Show course links using the helper function
      showCourseLinks(); // This function now appends the links generated by getCourseLinksHtml
    })
    .catch((error) => {
      console.error("Error:", error);
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer Antwort.",
      });
    });
}

function submitT3EmailResponse(event) {
  event.preventDefault();
  const emailElement = document.getElementById("t3EmailResponse");
  if (!emailElement) return;
  const email = emailElement.value.trim();

  // If the email field is empty, show a message that no email will be saved
  // but still proceed to the final thank you message
  if (!email) {
    Swal.fire({
      icon: "info",
      title: "Keine E-Mail angegeben",
      text: "Sie haben keine E-Mail-Adresse eingegeben. Wir werden Sie nicht kontaktieren.",
      confirmButtonText: "Fortfahren",
    }).then(() => {
      // Show the final thank you message without saving anything
      showFinalThankYouMessage();
    });
    return;
  }

  // If email is provided, validate it
  if (!isValidEmail(email)) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte geben Sie eine gültige E-Mail-Adresse ein.",
    });
    return;
  }

  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group = userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";

  console.log(`Submitting T3 email for Group ${group}`);

  // Disable input while saving
  emailElement.disabled = true;
  const submitButton = document.getElementById("submitT3EmailResponse");
  if (submitButton) submitButton.disabled = true;

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t3_email",
      response: email,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then(() => {
      // Show success message after saving email
      showFinalThankYouMessage(true); // true indicates email was saved
    })
    .catch((error) => {
      console.error("Error saving T3 email:", error);

      // Show error message but then proceed to thank you message anyway
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer E-Mail-Adresse. Wir werden trotzdem mit der Umfrage fortfahren.",
        confirmButtonText: "Fortfahren",
      }).then(() => {
        // Show thank you message even if email saving failed
        showFinalThankYouMessage(false);
      });

      // Re-enable input on error (though user won't see it anymore)
      emailElement.disabled = false;
      if (submitButton) submitButton.disabled = false;
    });
}

// Helper function to validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to show the final thank you message
function showFinalThankYouMessage(emailSaved = false) {
  const group = userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";
  let message = "";
  let title = emailSaved ? "Gespeichert" : "Vielen Dank";

  if (group === "Gruppe A") {
    message =
      'Danke für Ihre Teilnahme. Sie können die Website nun schließen oder zur <a href="index.html">Startseite</a> zurückkehren.';
  } else if (group === "Gruppe B") {
    message = `Danke für Ihre Teilnahme. Im Rahmen des Projekts <strong>Open-Digi</strong> haben wir Mikrofortbildungen erstellt, die Ihnen dabei helfen, Ihre digitalen Kompetenzen gezielt zu fördern. Wenn Sie Lust haben, sich in bestimmten Kompetenzbereichen weiterzuentwickeln, dann absolvieren Sie gerne unsere Mikrofortbildungen.<br><br>
${getCourseLinksHtml()}<br>
Hier gelangen Sie zurück zur <a href="index.html">Startseite</a>.`;
  }

  Swal.fire({
    icon: "success",
    title: title,
    html: message,
    confirmButtonText: "OK",
    didOpen: () => {
      if (typeof confetti === "function")
        confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
    },
  });
}

function submitT3AdditionalResponse(event) {
  event.preventDefault();
  const responseElement = document.getElementById("t3AdditionalResponse");
  if (!responseElement) return;
  const response = responseElement.value.trim();

  if (!response) {
    Swal.fire({
      icon: "error",
      title: "Fehler",
      text: "Bitte füllen Sie das Textfeld aus.",
    });
    return;
  }
  const userId = sessionStorage.getItem("userId");
  const attemptNumber = parseInt(
    sessionStorage.getItem("attemptNumber") || "1",
    10
  );
  const group = userData?.preSurveyResponses?.["q-2_1"] || "Gruppe A";

  console.log(
    `Submitting T3 additional reflection for Group ${group}. Response: "${response}"`
  );

  responseElement.disabled = true;
  const submitButton = document.getElementById("submitT3AdditionalResponse");
  if (submitButton) {
    submitButton.disabled = true;
    submitButton.textContent = "Wird gespeichert...";
  }

  fetch("php/save-open-ended-response.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      userId,
      key: "t3_additional_reflection",
      response,
      attempt: attemptNumber,
    }),
  })
    .then((res) => {
      if (!res.ok) throw new Error("Network response was not ok");
      return res.json();
    })
    .then((saveData) => {
      console.log("T3 additional reflection saved:", saveData);
      if (userData && userData.openEndedResponses) {
        userData.openEndedResponses["t3_additional_reflection"] = response;
      }

      // --- Group-Specific Final Step ---
      if (group === "Gruppe B") {
        console.log(
          "Group B: T3 additional reflection submitted. Preparing to show email prompt."
        );
        const emailValue = userData?.openEndedResponses?.t3_email || "";
        const surveyForm = document.getElementById("surveyForm");
        const resultsSection = surveyForm.querySelector(".results-section");

        if (resultsSection) {
          // Create container element
          const emailContainer = document.createElement("div");
          emailContainer.className = "final-inputs";

          // Create paragraph
          const paragraph = document.createElement("p");
          paragraph.innerHTML =
            "<strong>Sie sind nun am Ende des Forschungsprojektes angekommen. Wenn Sie Interesse an einem weiteren Austausch zu ihrer Lernerfahrung haben, tragen Sie im Folgenden bitte Ihre E-Mail Adresse ein und wir werden uns bei Ihnen melden.</strong>";
          emailContainer.appendChild(paragraph);

          // Create input group
          const inputGroup = document.createElement("div");
          inputGroup.className = "email-input-group";

          // Create email input
          const emailInput = document.createElement("input");
          emailInput.type = "email";
          emailInput.id = "t3EmailResponse";
          emailInput.value = emailValue;
          emailInput.placeholder = "Ihre E-Mail-Adresse (optional)";
          inputGroup.appendChild(emailInput);

          // Create submit button
          const submitButton = document.createElement("button");
          submitButton.id = "submitT3EmailResponse";
          submitButton.className = "btn btn-primary";
          submitButton.textContent = "Absenden";
          // Add event listener directly to the button
          submitButton.addEventListener("click", submitT3EmailResponse);
          console.log(
            "Group B: Event listener added directly to button during creation"
          );
          inputGroup.appendChild(submitButton);

          // Add input group to container
          emailContainer.appendChild(inputGroup);

          // Add container to results section
          resultsSection.appendChild(emailContainer);

          // Scroll to the email container
          if (typeof emailContainer.scrollIntoView === "function") {
            emailContainer.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
          setTimeout(() => {
            const emailSubmitButton = document.getElementById(
              "submitT3EmailResponse"
            );
            if (emailSubmitButton) {
              emailSubmitButton.addEventListener(
                "click",
                submitT3EmailResponse
              );
              console.log(
                "Group B: Event listener added to email submit button (submitT3EmailResponse)."
              );
            } else {
              console.error(
                "Group B: Email submit button (submitT3EmailResponse) not found after appending and timeout."
              );
              // Try again with a longer timeout
              setTimeout(() => {
                const retryEmailSubmitButton = document.getElementById(
                  "submitT3EmailResponse"
                );
                if (retryEmailSubmitButton) {
                  retryEmailSubmitButton.addEventListener(
                    "click",
                    submitT3EmailResponse
                  );
                  console.log(
                    "Group B: Event listener added to email submit button on second attempt."
                  );
                } else {
                  console.error(
                    "Group B: Email submit button still not found after second attempt."
                  );
                }
              }, 500); // 500ms timeout for second attempt
            }
          }, 100);
        } else {
          console.error(
            "Group B: .results-section not found. Cannot append email prompt."
          );
        }
      } else if (group === "Gruppe D") {
        // Group D gets course links and thank you message
        console.log(
          "Group D: T3 additional reflection submitted. Showing final message with course links."
        );
        Swal.fire({
          icon: "success",
          title: "Vielen Dank!",
          html: `Danke für Ihre Teilnahme. Im Rahmen des Projekts <strong>Open-Digi</strong> haben wir Mikrofortbildungen erstellt, die Ihnen dabei helfen, Ihre digitalen Kompetenzen gezielt zu fördern. Wenn Sie Lust haben, sich in bestimmten Kompetenzbereichen weiterzuentwickeln, dann absolvieren Sie gerne unsere Mikrofortbildungen.<br><br>
          ${getCourseLinksHtml()}<br>
          Hier können Sie auf die <a href="index.html">Startseite</a> zurück gelangen.`,
          confirmButtonText: "OK",
          didOpen: () => {
            if (typeof confetti === "function")
              confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
          },
        });
      } else {
        // Fallback for other groups (A, C should have already shown email prompt earlier)
        console.warn(
          "T3 additional reflection: Unhandled group for final step:",
          group
        );
        Swal.fire({
          icon: "success",
          title: "Vielen Dank!",
          html: 'Danke für Ihre Teilnahme. Sie können die Website nun schließen oder zur <a href="index.html">Startseite</a> zurückkehren.',
          confirmButtonText: "OK",
          didOpen: () => {
            if (typeof confetti === "function")
              confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
          },
        });
      }

      // --- Clear the sessionStorage flags ---
      if (group === "Gruppe B") {
        sessionStorage.removeItem("t3FirstStepCompleted_B");
        console.log("Cleared Group B T3 step flag.");
      } else if (group === "Gruppe D") {
        sessionStorage.removeItem("t3FirstStepCompleted_D");
        console.log("Cleared Group D T3 step flag.");
      }
    })
    .catch((error) => {
      console.error("Error saving T3 additional reflection:", error);
      Swal.fire({
        icon: "error",
        title: "Fehler",
        text: "Es gab einen Fehler beim Speichern Ihrer Antwort.",
      });
      responseElement.disabled = false;
      if (submitButton) {
        submitButton.disabled = false;
        submitButton.textContent = "Absenden";
      }
    });
}

function getCourseLinksHtml() {
  return `
        <div class="ilias-links">
            <a href="https://ilias.uni-rostock.de/goto.php?target=crs_135810&client_id=ilias_hro" target="_blank" class="course-link-box">
              <div class="course-link-content">
                <h3>Hier haben Sie direkten Zugriff auf die Mikrofortbildungen für alle Kompetenzbereiche</h3>
                <span class="click-hint">Klicken Sie hier, um zu den Kursen zu gelangen</span>
              </div>
            </a>
        </div>`;
}

/**
 * Appends the ILIAS course links to the results section after T1 reflection submission.
 * Inserts the links after the T1 reflection textarea.
 */
function showCourseLinks() {
  // Find the T1 reflection textarea element
  const reflectionTextarea = document.getElementById("t1OpenEndedResponse");

  if (!reflectionTextarea) {
    console.warn(
      "T1 reflection textarea (#t1OpenEndedResponse) not found, cannot insert course links."
    );
    return; // Exit if the textarea isn't found
  }

  // Check if links already exist to prevent duplicates
  if (document.querySelector(".course-links-container")) {
    console.log("Course links already exist, skipping insertion.");
    return;
  }

  // Create a container for the links
  const courseLinksContainer = document.createElement("div");
  courseLinksContainer.className = "course-links-container"; // Add a wrapper class

  // Add the links HTML using the helper function
  courseLinksContainer.innerHTML = getCourseLinksHtml(); // Uses the new single link box

  // Insert the new container *directly after* the reflection textarea element
  reflectionTextarea.insertAdjacentElement("afterend", courseLinksContainer);

  console.log("Course links inserted after T1 reflection textarea.");

  // Add confetti effect
  if (typeof confetti === "function") {
    confetti({ particleCount: 100, spread: 70, origin: { y: 0.6 } });
  }
}

window.handleScaleClick = handleScaleClick;
window.handleScaleKeydown = handleScaleKeydown;
window.showResults = showResults;
window.validateYearInput = validateYearInput;
window.validateSemesterInput = validateSemesterInput;
